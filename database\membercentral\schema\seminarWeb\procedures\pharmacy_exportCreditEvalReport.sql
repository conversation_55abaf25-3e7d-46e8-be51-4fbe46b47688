ALTER PROC dbo.pharmacy_exportCreditEvalReport
@sponsorID int,
@publishingOrg varchar(15),
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
	SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0);
	SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0);

	IF OBJECT_ID('tempdb..#tmpEvalResults') IS NOT NULL 
		DROP TABLE #tmpEvalResults;
	IF OBJECT_ID('tempdb..#tmpEvalResultsPVT') IS NOT NULL 
		DROP TABLE #tmpEvalResultsPVT;
	IF OBJECT_ID('tempdb..#tmpPharmacyCredit') IS NOT NULL 
		DROP TABLE #tmpPharmacyCredit;

	DECLARE @tmpQuestions TABLE (qNum varchar(4) PRIMARY KEY, questionText varchar(400), exportLabel varchar(100), offerOpts bit);

	INSERT INTO @tmpQuestions (qNum, questionText, exportLabel, offerOpts)
	SELECT 'q0', 'Overall quality of this activity', 'Overall quality', 1
		UNION
	SELECT 'q1', 'How well did this activity meet your individual educational needs?', 'How well did this activity meet your individual educational needs?', 1
		UNION
	SELECT 'q2', 'Value of the content', 'Value of the content', 1
		UNION
	SELECT 'q3', 'Please rate the effectiveness of the active learning exercises.', 'Please rate the effectiveness of the active learning exercises', 1
		UNION
	SELECT 'q4', 'Please rate the appropriateness of the final exam questions.', 'Please rate the appropriateness of the final exam questions', 1
		UNION
	SELECT 'q5', 'Please rate the usefulness of the educational materials used during this activity (e.g. handouts)', 'Please rate the usefulness of the educational materials', 1
		UNION
	SELECT 'q6', 'The information presented reinforced my current practice/treatment habits', 'Information reinforced my current practice/treatment habits', 1
		UNION
	SELECT 'q7', 'The information presented will improve my practice/patient outcomes', 'Information will improve my practice/patient outcomes', 1
		UNION
	SELECT 'q8', 'The information presented provided new ideas or information I expect to use.', 'Information provided new ideas or information I expect to use', 1
		UNION
	SELECT 'q9', 'The information enhanced my current knowledge base.', 'Information enhanced my current knowledge base', 1
		UNION
	SELECT 'q10', 'The program increased my knowledge in the subject areas.', 'Program increased my knowledge in the subject areas', 1
		UNION
	SELECT 'q11', 'I feel future activities on this subject matter are necessary and/or important to my practice.', 'Future activities on this subject matter are necessary and/or important to my practice', 1
		UNION
	SELECT 'q12', 'The program did not promote a particular product or company.', 'Program did not promote a particular product or company', 1
		UNION
	SELECT 'q13', 'Did this activity meet the stated learning objectives?', 'Did this activity meet the stated learning objectives?', 1
		UNION
	SELECT 'q14', 'Will the information presented cause you to make any changes in your practice?', 'Will the information presented cause you to make any changes in your practice?', 1
		UNION
	SELECT 'q15', 'Please list 1-2 things you will do differently:', 'Please list 1-2 things you will do differently:', 0
		UNION
	SELECT 'q16', 'How committed are you to making these changes?', 'How committed are you to making these changes?', 1
		UNION
	SELECT 'q17', 'What aspects of this training activity did you enjoy most?', 'What aspects of this training activity did you enjoy most?', 0
		UNION
	SELECT 'q18', 'What aspects of this training activity did you enjoy least?', 'What aspects of this training activity did you enjoy least?', 0
		UNION
	SELECT 'q19', 'Please provide any additional comments about the seminar (including anything you would change for future offerings):', 'Please provide any additional comments about the seminar', 0
		UNION
	SELECT 'q20', 'As part of our ongoing quality-improvement effort, we would like to be able to contact you in the event we conduct a follow-up survey to assess the impact of our educational interventions on professional practice. Are you willing to participate in such a survey?', 'Are you willing to participate in a follow-up survey?', 1
		UNION
	SELECT 'q21', 'Knowledge of subject matter', 'Knowledge of subject matter', 1
		UNION
	SELECT 'q22', 'Communication and presentation skills', 'Communication and presentation skills', 1
		UNION
	SELECT 'q23', 'Overall responsiveness to audience''s questions', 'Overall responsiveness to audience''s questions', 1
		UNION
	SELECT 'q24', 'Overall speaker quality', 'Overall speaker quality', 1
		UNION
	SELECT 'q42', 'For CE reporting purposes, please enter the month you were born. Month must be in two digital format (ex. 02 or 15).', 'Birth Month', 0
		UNION
	SELECT 'q43', 'For CE reporting purposes, please enter the day of the month you were born. Day must be in two digital format (ex. 02 or 15).', 'Birth Date', 0;

	-- credit report for anyone who picked passed in credit
	-- SWOD only
	select min(safr.enrollmentID) as enrollmentID, rd.questionID, tmp.qNum,
		case when tmp.qNum in ('q42','q43','q15','q17','q18','q19') then rd.responseText
		else o.optionText end as pivotText
	into #tmpEvalResults
	from seminarweb.dbo.tblSeminarsAndFormResponses as safr
	inner join seminarweb.dbo.tblenrollments as e on e.enrollmentid = safr.enrollmentid
		and e.passed = 1 
		and e.isactive = 1
		and e.datecompleted between @startdate and @enddate
	inner join seminarweb.dbo.tblSeminarsSWOD as sswod on sswod.seminarID = e.seminarID
	inner join seminarweb.dbo.tblSeminars as s on s.seminarID = sswod.seminarID
	inner join seminarweb.dbo.tblParticipants as pPublisher on pPublisher.participantID = s.participantID
		and pPublisher.orgCode = @publishingOrg
	inner join seminarweb.dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
		and eac.earnedCertificate = 1
	inner join seminarweb.dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
	inner join seminarweb.dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
		and csa.sponsorID = @sponsorID
	inner join formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
		and r.isActive = 1
	inner join formBuilder.dbo.tblForms as f on f.formID = r.formID
	inner join formBuilder.dbo.tblFormTypes as ft on ft.formTypeID = f.formTypeID
		and ft.formTypeAbbr = 'S'
		and f.formTitle = 'Pharmacy Evaluation Form'
		and f.isDeleted = 0
	inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseID = safr.responseID
	inner join formBuilder.dbo.tblQuestions as q on q.questionID = rd.questionID
	inner join @tmpQuestions as tmp on tmp.questionText = q.questionText
	LEFT OUTER JOIN formbuilder.dbo.tblOptions o on rd.optionID = o.optionID
	GROUP BY safr.enrollmentID, rd.questionID, tmp.qNum, rd.optionID, optionXID,
			rd.responseText, o.optionText;

	-- fix question ids
	WITH CTE AS (
		SELECT x.questionID, x.formerQuestionID, x.questionID as topID
		FROM formBuilder.dbo.tblQuestions x
		INNER JOIN #tmpEvalResults t on t.questionID = x.questionID
			UNION ALL
		SELECT s.questionID, s.formerQuestionID, CTE.topID
		FROM formBuilder.dbo.tblQuestions as s
		JOIN CTE ON s.questionID = CTE.formerQuestionID
	)
	update t
	set t.questionID = r.questionID,
		t.qNum = tmp.qNum
	from CTE r
	inner join #tmpEvalResults t on t.questionID = r.topID
	inner join formBuilder.dbo.tblQuestions as q on q.questionID = r.questionID
	inner join @tmpQuestions as tmp on tmp.questionText = q.questionText
	where r.formerQuestionID is null 
	and r.questionID <> r.topID;

	-- pivot eval data
	select enrollmentID, [q42], [q43], [q0], [q1], [q2], [q3], [q4], [q5], [q6], [q7], [q8], [q9], [q10],
		[q11], [q12], [q13], [q14], [q15], [q16], [q17], [q18], [q19], [q20], [q21], [q22], [q23], [q24]
	into #tmpEvalResultsPVT
	from (
		select enrollmentID, pivotText, qNum
		from #tmpEvalResults
	) as tmp
	PIVOT (min(pivotText) FOR qNum in ([q42],[q43],[q0],[q1],[q2],[q3],[q4],[q5],[q6],[q7],[q8],[q9],[q10],
		[q11],[q12],[q13],[q14],[q15],[q16],[q17],[q18],[q19],[q20],[q21],[q22],[q23],[q24])) as pvt;

	select sem.seminarID as [Seminar ID],
		'On-Demand' as [Program Type], 
		sem.seminarname as [Seminar Name], 
		sac.courseApproval as [Course Approval Num], 
		eac.idnumber as [ID Number],
		d.lastname as [Last Name],
		d.firstname as [First Name],
		isnull(o.address1,d.billingAddress) as [Address Line 1],
		isnull(o.address2,d.billingAddress2) as [Address Line 2],
		isnull(o.address3,d.billingAddress3) as [Address Line 3],
		isnull(o.city,d.billingCity) as City,
		isnull(o.state,d.billingState) as State,
		isnull(o.zipcode,d.billingZIP) as ZIP,
		d.phone as Phone,
		d.email as [E-Mail],
		convert(varchar(10),e.dateenrolled,101) as [Date Enrolled],
		convert(varchar(10),e.datecompleted,101) as [Date Completed],
		eac.finaltimespent as [Minutes Spent in Course],
		qPvt.[q42] as [Birth Month],
		qPvt.[q43] as [Birth Date],
		qPvt.[q0] as [Overall quality],
		qPvt.[q1] as [How well did this activity meet your individual educational needs?],
		qPvt.[q2] as [Value of the content],
		qPvt.[q3] as [Please rate the effectiveness of the active learning exercises],
		qPvt.[q4] as [Please rate the appropriateness of the final exam questions],
		qPvt.[q5] as [Please rate the usefulness of the educational materials],
		qPvt.[q6] as [Information reinforced my current practice/treatment habits],
		qPvt.[q7] as [Information will improve my practice/patient outcomes],
		qPvt.[q8] as [Information provided new ideas or information I expect to use],
		qPvt.[q9] as [Information enhanced my current knowledge base],
		qPvt.[q10] as [Program increased my knowledge in the subject areas],
		qPvt.[q11] as [Future activities on this subject matter are necessary and/or important to my practice],
		qPvt.[q12] as [Program did not promote a particular product or company],
		qPvt.[q13] as [Did this activity meet the stated learning objectives?],
		qPvt.[q14] as [Will the information presented cause you to make any changes in your practice?],
		qPvt.[q15] as [Please list 1-2 things you will do differently:],
		qPvt.[q16] as [How committed are you to making these changes?],
		qPvt.[q17] as [What aspects of this training activity did you enjoy most?],
		qPvt.[q18] as [What aspects of this training activity did you enjoy least?],
		qPvt.[q19] as [Please provide any additional comments about the seminar],
		qPvt.[q20] as [Are you willing to participate in a follow-up survey?],
		qPvt.[q21] as [Knowledge of subject matter],
		qPvt.[q22] as [Communication and presentation skills],
		qPvt.[q23] as [Overall responsiveness to audience's questions],
		qPvt.[q24] as [Overall speaker quality]
	INTO #tmpPharmacyCredit
	FROM tblenrollments as e
	INNER JOIN tblParticipants p on e.participantID = p.participantID
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	inner join dbo.tblParticipants as pPublisher on pPublisher.participantID = sem.participantID
	INNER JOIN tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	INNER JOIN tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
	INNER JOIN tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
	INNER JOIN tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid 
		AND o.orgcode = cs.orgcode
	LEFT OUTER JOIN #tmpEvalResultsPVT as qPvt on qPvt.enrollmentid = e.enrollmentid
	WHERE e.passed = 1 
	and eac.earnedCertificate = 1
	AND e.datecompleted between @startdate and @enddate
	and pPublisher.orgCode = @publishingOrg
	ORDER BY e.datecompleted;

	IF OBJECT_ID('tempdb..#tmpEvalResults') IS NOT NULL 
		DROP TABLE #tmpEvalResults;
	IF OBJECT_ID('tempdb..#tmpEvalResultsPVT') IS NOT NULL 
		DROP TABLE #tmpEvalResultsPVT;


	-- Add SemWeb Live
	-- SWL only
	IF OBJECT_ID('tempdb..#tmpSWEvalResults') IS NOT NULL 
		DROP TABLE #tmpSWEvalResults;
	IF OBJECT_ID('tempdb..#tmpSWEvalResults2') IS NOT NULL 
		DROP TABLE #tmpSWEvalResults2;
	IF OBJECT_ID('tempdb..#tmpSWEvalResultsPVT') IS NOT NULL 
		DROP TABLE #tmpSWEvalResultsPVT;
	IF OBJECT_ID('tempdb..#tmpSWLCredit') IS NOT NULL 
		DROP TABLE #tmpSWLCredit;
	IF OBJECT_ID('tempdb..#tmpE') IS NOT NULL 
		DROP TABLE #tmpE;

	select min(safr.enrollmentID) as enrollmentID, rd.questionID, tmp.qNum, rd.responseText, o.optionText, max(r.datecompleted) as datecompleted
	into #tmpSWEvalResults
	from seminarweb.dbo.tblSeminarsAndFormResponses as safr
	inner join seminarweb.dbo.tblenrollments as e on e.enrollmentid = safr.enrollmentid
		and e.passed = 1 
		and e.isactive = 1
		and e.datecompleted between @startdate and @enddate
	inner join seminarweb.dbo.tblSeminarsSWLive as swl on swl.seminarID = e.seminarID
	inner join seminarweb.dbo.tblSeminars as s on s.seminarID = swl.seminarID
	inner join seminarweb.dbo.tblParticipants as pPublisher on pPublisher.participantID = s.participantID
		and pPublisher.orgCode = @publishingOrg
	inner join seminarweb.dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
		and eac.earnedCertificate = 1
	inner join seminarweb.dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
	inner join seminarweb.dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
		and csa.sponsorID = @sponsorID
	inner join formbuilder.dbo.tblResponses as r on r.responseID = safr.responseID
		and r.isActive = 1
		and r.dateCompleted is not null
	inner join formBuilder.dbo.tblForms as f on f.formID = r.formID
	inner join formBuilder.dbo.tblFormTypes as ft on ft.formTypeID = f.formTypeID
		and ft.formTypeAbbr = 'S'
		and f.formTitle = 'Pharmacy Evaluation Form'
		and f.isDeleted = 0
	inner join formbuilder.dbo.tblResponseDetails as rd on rd.responseID = safr.responseID
	inner join formBuilder.dbo.tblQuestions as q on q.questionID = rd.questionID
	inner join @tmpQuestions as tmp on tmp.questionText = q.questionText
	LEFT OUTER JOIN formbuilder.dbo.tblOptions o on rd.optionID = o.optionID
	GROUP BY safr.enrollmentID, rd.questionID, tmp.qNum, rd.optionID, optionXID, rd.responseText, o.optionText
	order by enrollmentid, datecompleted, questionid;

	-- fix question ids
	WITH CTE AS	(
		SELECT x.questionID, x.formerQuestionID, x.questionID as topID
		FROM formBuilder.dbo.tblQuestions x
		INNER JOIN #tmpSWEvalResults t on t.questionID = x.questionID
		UNION ALL
		SELECT s.questionID, s.formerQuestionID, CTE.topID
		FROM formBuilder.dbo.tblQuestions as s
		JOIN CTE ON s.questionID = CTE.formerQuestionID
	)
	update t
	set t.questionID = r.questionID,
		t.qNum = tmp.qNum
	from CTE r
	inner join #tmpSWEvalResults t on t.questionID = r.topID
	inner join formBuilder.dbo.tblQuestions as q on q.questionID = r.questionID
	inner join @tmpQuestions as tmp on tmp.questionText = q.questionText
	where r.formerQuestionID is null 
	and r.questionID <> r.topID;

	-- Take the latest date for processing
	select distinct enrollmentid, max(dateCompleted) as dateCompleted 
	into #tmpE 
	from #tmpSWEvalResults 
	where datecompleted is not null 
	group by enrollmentid;

	-- Remove duplicate responses
	delete r
	from #tmpSWEvalResults r
	inner join #tmpE m on m.enrollmentid = r.enrollmentid 
		and m.dateCompleted <> r.dateCompleted;

	select enrollmentID, questionID, qNum,
		case when qNum in ('q42','q43','q15','q17','q18','q19') then responseText
		else optionText end as pivotText
	into #tmpSWEvalResults2
	from #tmpSWEvalResults;

	-- pivot eval data
	select enrollmentID, [q42], [q43], [q0], [q1], [q2], [q3], [q4], [q5], [q6], [q7], [q8], [q9], [q10],
		[q11], [q12], [q13], [q14], [q15], [q16], [q17], [q18], [q19], [q20], [q21], [q22], [q23], [q24]
	into #tmpSWEvalResultsPVT
	from (
		select enrollmentID, pivotText, qNum
		from #tmpSWEvalResults2
	) as tmp
	PIVOT (min(pivotText) FOR qNum in ([q42],[q43],[q0],[q1],[q2],[q3],[q4],[q5],[q6],[q7],[q8],[q9],[q10],
		[q11],[q12],[q13],[q14],[q15],[q16],[q17],[q18],[q19],[q20],[q21],[q22],[q23],[q24])) as pvt;

	insert into #tmpPharmacyCredit
	select sem.seminarID as [Seminar ID], 
		'Webinar' as [Program Type], 
		sem.seminarname as [Seminar Name], 
		sac.courseApproval as [Course Approval Num], 
		eac.idnumber as [ID Number],
		d.lastname as [Last Name],
		d.firstname as [First Name],
		isnull(o.address1,d.billingAddress) as [Address Line 1],
		isnull(o.address2,d.billingAddress2) as [Address Line 2],
		isnull(o.address3,d.billingAddress3) as [Address Line 3],
		isnull(o.city,d.billingCity) as City,
		isnull(o.state,d.billingState) as State,
		isnull(o.zipcode,d.billingZIP) as ZIP,
		d.phone as Phone,
		d.email as [E-Mail],
		convert(varchar(10),e.dateenrolled,101) as [Date Enrolled],
		convert(varchar(10),r.datecompleted,101) as [Date Completed],
		eac.finaltimespent as [Minutes Spent in Course],
		qPvt.[q42] as [Birth Month],
		qPvt.[q43] as [Birth Date],
		qPvt.[q0] as [Overall quality],
		qPvt.[q1] as [How well did this activity meet your individual educational needs?],
		qPvt.[q2] as [Value of the content],
		qPvt.[q3] as [Please rate the effectiveness of the active learning exercises],
		qPvt.[q4] as [Please rate the appropriateness of the final exam questions],
		qPvt.[q5] as [Please rate the usefulness of the educational materials],
		qPvt.[q6] as [Information reinforced my current practice/treatment habits],
		qPvt.[q7] as [Information will improve my practice/patient outcomes],
		qPvt.[q8] as [Information provided new ideas or information I expect to use],
		qPvt.[q9] as [Information enhanced my current knowledge base],
		qPvt.[q10] as [Program increased my knowledge in the subject areas],
		qPvt.[q11] as [Future activities on this subject matter are necessary and/or important to my practice],
		qPvt.[q12] as [Program did not promote a particular product or company],
		qPvt.[q13] as [Did this activity meet the stated learning objectives?],
		qPvt.[q14] as [Will the information presented cause you to make any changes in your practice?],
		qPvt.[q15] as [Please list 1-2 things you will do differently:],
		qPvt.[q16] as [How committed are you to making these changes?],
		qPvt.[q17] as [What aspects of this training activity did you enjoy most?],
		qPvt.[q18] as [What aspects of this training activity did you enjoy least?],
		qPvt.[q19] as [Please provide any additional comments about the seminar],
		qPvt.[q20] as [Are you willing to participate in a follow-up survey?],
		qPvt.[q21] as [Knowledge of subject matter],
		qPvt.[q22] as [Communication and presentation skills],
		qPvt.[q23] as [Overall responsiveness to audience's questions],
		qPvt.[q24] as [Overall speaker quality]
	FROM tblenrollments e
	INNER JOIN #tmpE as r on r.enrollmentid = e.enrollmentid
	INNER JOIN tblParticipants p on e.participantID = p.participantID
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	inner join dbo.tblParticipants as pPublisher on pPublisher.participantID = sem.participantID
	INNER JOIN tblSeminarsSWLive as swl on swl.seminarID = sem.seminarID
	INNER JOIN tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid and cs.sponsorID = @sponsorID
	INNER JOIN tblCreditAuthorities as ca on ca.authorityid = csa.authorityid
	INNER JOIN tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid 
		AND o.orgcode = cs.orgcode
	LEFT OUTER JOIN #tmpSWEvalResultsPVT as qPvt on qPvt.enrollmentid = e.enrollmentid
	WHERE e.passed = 1 
	and eac.earnedCertificate = 1
	AND r.datecompleted between @startdate and @enddate
	and pPublisher.orgCode = @publishingOrg
	ORDER BY e.datecompleted;

	IF OBJECT_ID('tempdb..#tmpSWEvalResults') IS NOT NULL 
		DROP TABLE #tmpSWEvalResults;
	IF OBJECT_ID('tempdb..#tmpSWEvalResults2') IS NOT NULL 
		DROP TABLE #tmpSWEvalResults2;
	IF OBJECT_ID('tempdb..#tmpSWEvalResultsPVT') IS NOT NULL 
		DROP TABLE #tmpSWEvalResultsPVT;
	IF OBJECT_ID('tempdb..#tmpSWLCredit') IS NOT NULL 
		DROP TABLE #tmpSWLCredit;
	IF OBJECT_ID('tempdb..#tmpE') IS NOT NULL 
		DROP TABLE #tmpE;

	-- return count
	declare @tblCount int;
	select @tblCount = count(*) from #tmpPharmacyCredit;
	select @tblCount as returnCount;

	-- export data
	IF @tblCount > 0 BEGIN
		DECLARE @selectsql varchar(max) = '
			SELECT *, ROW_NUMBER() OVER(order by [Seminar ID]) as mcCSVorder 
			*FROM* #tmpPharmacyCredit';
		EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@filename, @returnColumns=0;
	END

	IF OBJECT_ID('tempdb..#tmpPharmacyCredit') IS NOT NULL 
		DROP TABLE #tmpPharmacyCredit;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
