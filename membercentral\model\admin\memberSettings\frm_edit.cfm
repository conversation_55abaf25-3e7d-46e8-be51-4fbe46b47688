<cfsavecontent variable="local.memberSettingsJS">
<cfoutput>
<script language="javascript">
	var #ToScript(local.permsGotoLink,"mca_perms_link")#
	var classificationsTable, linkedClassificationsTable, reqReload = 0;

	function validateSettingsForm() {
		hideAlert();
		var arrReq = new Array();
		if ($('##SearchFieldSet').val() == '0' || $('##SearchFieldSet').val().length == 0) arrReq[arrReq.length] = 'Select which Member Field Set to use when searching for members.';
		if ($('##ResultsFieldSet').val() == '0' || $('##ResultsFieldSet').val().length == 0) arrReq[arrReq.length] = 'Select which Member Field Set to use when showing search results.';

		if (arrReq.length > 0) {
			var msg = '<strong>The following requires your attention:</strong><br/>' + arrReq.join('<br/>');
			showAlert(msg);
			return false;
		}
		$('##btnSaveSettings').attr('disabled',true);
		return true;
	}	
	function removeClassification(cID,isLinked) {
		var removeResult	= function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				if (isLinked) linkedClassificationsTable.draw(false);
				else classificationsTable.draw(false);
			} else {
				alert('We were unable to delete this Classification.');
				delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			}
		};

		let delBtn = $('##btnDelGS'+cID);
		mca_initConfirmButton(delBtn, function(){
			var objParams = { classificationID:cID };
			TS_AJX('ADMMEMBERSETTINGS','deleteClassification',objParams,removeResult,removeResult,10000,removeResult);
		});
	}
	function editClassification(cID,srID) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'md',
			title: cID > 0 ? 'Edit Classification' : 'Add Classification',
			iframe: true,
			contenturl: '#this.link.editClassification#&classificationID=' + cID + '&siteResourceID=' + srID,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmClassification :submit").click',
				extrabuttonlabel: 'Save'
			}
		});
	}
	function refreshGrids() {
		reloadFieldSetsGridForCustomTab();
		reloadFieldSetsGridForCopyInfo();
	}
	function closeBox() { 
		if (reqReload == 1) top.location.href=top.location.href;
		else MCModalUtils.hideModal(); 
		reqReload = 0;
	}
	function hideAlert() { $('##settings_err').html('').addClass('d-none'); };
	function showAlert(msg) { $('##settings_err').html(msg).removeClass('d-none'); };

	/* functions for field set selector widget */
	function reloadFieldSetsGridForCustomTab(){
		loadFieldSetGrids_#local.strCustomFieldsSelector.selectorID#();
	}
	function getAvailableAndSelectedFieldSetsForCustomTab(onCompleteFunc){
		let objParams = { siteResourceID:#this.memberAdminSiteResourceID#, area:'custom' };
		TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
	}
	function createMemberFieldUsageForCustomTab(fsID, onCompleteFunc) {
		let objParams = { fieldSetID:fsID, siteResourceID:#this.memberAdminSiteResourceID# };
		TS_AJX('ADMMEMBERSETTINGS','createCustomMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
	}
	function moveCustomFSRow(useID, dir, onCompleteFunc) {
		var objParams = { useID:useID, dir:dir };
		TS_AJX('FIELDSETWIDGET','fsMove',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
	}
	function reloadFieldSetsGridForCopyInfo(){
		loadFieldSetGrids_#local.strCopyInfoFieldsSelector.selectorID#();
	}
	function getAvailableAndSelectedFieldSetsForCopyInfo(onCompleteFunc){
		let objParams = { siteResourceID:#this.memberAdminSiteResourceID#, area:'copyinfo' };
		TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
	}
	function createMemberFieldUsageForCopyInfo(fsID, onCompleteFunc) {
		let objParams = { fieldSetID:fsID, siteResourceID:#this.memberAdminSiteResourceID#, area:'copyinfo' };
		TS_AJX('FIELDSETWIDGET','createMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
	}
	function removeMemberFieldUsage(useID, onCompleteFunc) {
		var objParams = { useID:useID };
		TS_AJX('FIELDSETWIDGET','fsRemove',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
	}

	function reloadClassificationsTable(){
		classificationsTable.draw(false);
		linkedClassificationsTable.draw(false);
	}

	function initClassificationsTable(tableName){
		if(tableName == 'linkedClassificationsTable') {
			var isLinked = true;
			var #ToScript(local.linkedRecordClassificationsLink,'lnk_classifications')#
		} else {
			var isLinked = false;
			var #ToScript(local.classificationsLink,'lnk_classifications')#
		}

		window[tableName] = $('##'+tableName).DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"info" : false,
			"language": {
				"lengthMenu": "_MENU_"
			},
			"ajax": { 
				"url": lnk_classifications,
				"type": "post",
				"data": function(d) { 
							if (window.reorderData && window.reorderData.length > 0) { 
								d.reorderData = JSON.stringify(window.reorderData); 
								window.reorderData = [];
							} 
							return d; 
					}
				
			},
			"autoWidth": false,
			"columns": [
				{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<i class="fa-light fa-bars"></i>';
								
							}
							return type === 'display' ? renderData : data;
						},
						"width": "5%",
						"orderable": false
					},
				{ "data": "classificationname", className: "align-top", "width": "80%" , "orderable": false},
				{ "data": null,
					"render": function ( data, type, row, meta ) {
						let renderData = '';
						if (type === 'display') {
							renderData += '<a href="" class="btn btn-xs text-primary p-1 m-1" title="Edit Group Set" onclick="editClassification('+data.classificationid+');return false;"><i class="fa-solid fa-pencil"></i></a>';
							renderData += '<a href="" id="btnDelGS'+data.classificationid+'" class="btn btn-xs text-danger p-1 m-1" title="Delete Group Set" onclick="removeClassification('+data.classificationid+','+isLinked+');return false;" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>';
						}
						return type === 'display' ? renderData : data;
					},
					"width": "15%",
					"className": "text-center",
					"orderable": false
				}
			],
			"ordering": false,
			"rowReorder": {
					dataSrc: "columnid" 
				},
			"searching": false
		});
		window[tableName].on('row-reorder', function (e, diff, edit) {
				let orderData = [];
				diff.forEach(function(item){
					orderData.push({
						id: window[tableName].row(item.node).data().classificationid,
						newOrder: item.newPosition
					});
				});
				window.reorderData = orderData;
			});
	}

	$(function() {
		initClassificationsTable('classificationsTable');
		initClassificationsTable('linkedClassificationsTable');
	});
</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.memberSettingsJS)#">

<cfoutput>
<form name="formSettings" id="formSettings" method="post" action="#this.link.saveSettings#" onsubmit="return validateSettingsForm();">
<input type="hidden" name="searchUseID" id="searchUseID" value="#local.searchUseID#">
<input type="hidden" name="resultsUseID" id="resultsUseID" value="#local.resultsUseID#">
<input type="hidden" name="newAcctUseID" id="newAcctUseID" value="#local.newacctUseID#">
<input type="hidden" name="mainUseID" id="mainUseID" value="#local.mainUseID#">
<input type="hidden" name="linkedRecordUseID" id="linkedRecordUseID" value="#local.linkedRecordUseID#">

<div class="row">
	<div class="col-auto">
		<h4>Member Settings</h4>
	</div>
	<div class="col text-right">
		<button type="button" class="btn btn-sm btn-secondary" onClick="mca_showPermissions(#this.siteResourceID#,'Member Settings');">Permissions</button>
		<button type="submit" name="btnSaveSettings" id="btnSaveSettings" class="btn btn-sm btn-primary">Save Settings</button>
	</div>
</div>

<div id="settings_err" class="alert alert-danger mb-2 d-none"></div>
<div class="card card-box mt-3">
	<div class="card-header py-2 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Searching for Members in Member Admin</div>
	</div>
	<div class="card-body pb-3">
		<div class="form-group row">
			<div class="col-sm-3">Use this Member Field Set<br/>when searching for members</div>
			<div class="col-sm-9">
				#local.strSearchFSSelector.html#
			</div>
		</div>
		<div class="form-group row mt-3">
			<div class="col-sm-3">Use this Member Field Set<br/>when showing search results</div>
			<div class="col-sm-9">
				#local.strResultsFSSelector.html#
				<div class="font-size-sm mt-1 ml-2">(First Name, Last Name, MemberNumber, and Company always appear.)</div>
			</div>
		</div>
		<div class="form-group row mt-2">
			<label for="showMemberPhotosInSearchResults" class="col-sm-3">Show Member Photos<br/>when showing search results</label>
			<div class="col-sm-9">
				<select name="showMemberPhotosInSearchResults" id="showMemberPhotosInSearchResults" class="form-control form-control-sm">
					<option value="true" <cfif local.showMemberPhotosInSearchResults eq "true">selected</cfif>>Yes, Show Photos</option>
					<option value="false" <cfif local.showMemberPhotosInSearchResults eq "false">selected</cfif>>No, Do Not Show Photos</option>
				</select>
			</div>
		</div>
		<div class="form-group row mt-2">
			<label for="numPerPageInSearchResults" class="col-sm-3">Number of results per page<br/>when showing search results</label>
			<div class="col-sm-9">
				<select name="numPerPageInSearchResults" id="numPerPageInSearchResults" class="form-control form-control-sm">
					<cfloop list="5,10,25" index="local.thisAmt">
						<option value="#local.thisAmt#" <cfif local.numPerPageInSearchResults eq local.thisAmt>selected</cfif>>#local.thisAmt# per page</option>
					</cfloop>
				</select>
			</div>
		</div>
	</div>
</div>
<div class="card card-box mt-4">
	<div class="card-header py-2 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Group Sets</div>
		<div>
			<button type="button" name="btnGroupSet" id="btnGroupSet" class="btn btn-sm btn-secondary" onclick="javascript:editClassification(0,#this.memberAdminSiteResourceID#);">
				<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
				<span class="btn-wrapper--label">Add Group Set</span>
			</button>
		</div>
	</div>
	<div class="card-body pb-3">
		<table id="classificationsTable" class="table table-sm table-striped table-bordered" style="width:100%">
			<thead>
				<tr>
					<th id="columnid"></th>
					<th>Group Set</th>
					<th>Actions</th>
				</tr>
			</thead>
		</table>
	</div>
</div>

<div class="card card-box mt-4">
	<div class="card-header py-2 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Managing Members in Member Admin</div>
	</div>
	<div class="card-body pb-3">
		<div class="form-group row">
			<div class="col-sm-3">Use this Member Field Set<br/>when showing the "Main" tab</div>
			<div class="col-sm-9">
				<input type="hidden" name="origMainFieldSet" id="origMainFieldSet" value="#local.mainFieldsetID#" />
				#local.strMainTabFSSelector.html#
			</div>
		</div>
		<div class="form-group row mt-4">
			<div class="col-sm-3">Use these Member Field Sets<br/>when showing the "Custom" tab</div>
			<div class="col-sm-9">
				#local.strCustomFieldsSelector.html#
			</div>
		</div>
		<div class="form-group row mt-4">
			<div class="col-sm-3">Use these Member Field Sets<br/>for the "Copy Info" action</div>
			<div class="col-sm-9">
				#local.strCopyInfoFieldsSelector.html#
			</div>
		</div>
	</div>
</div>
<div class="card card-box mt-4">
	<div class="card-header py-2 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Adding Members in Member Admin</div>
	</div>
	<div class="card-body pb-3">
		<div class="form-group row">
			<div class="col-sm-3">Use this Member Field Set<br/>when showing the "Custom" tab<br/>while adding new members</div>
			<div class="col-sm-9">
				<input type="hidden" name="origNewAcctFieldSet" id="origNewAcctFieldSet" value="#local.newacctFieldsetID#" />
				#local.strNewAcctFSSelector.html#
			</div>
		</div>
	</div>
</div>
<div class="card card-box mt-4">
	<div class="card-header py-2 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Adding Field Sets in Linked Records Section</div>
	</div>
	<div class="card-body pb-3">
		<div class="form-group row">
			<div class="col-sm-3">Use this Member Field Set<br/>when showing Linked Records</div>
			<div class="col-sm-9">
				<input type="hidden" name="origLinkedRecordFieldSet" id="origLinkedRecordFieldSet" value="#local.linkedRecordFieldsetID#" />
				#local.strLinkedRecordFSSelector.html#
			</div>
		</div>
	</div>
</div>
<div class="card card-box mt-4">
	<div class="card-header py-2 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Linked Records Group Sets</div>
		<div>
			<button type="button" name="btnGroupSet" id="btnLinkedRecordsGroupSet" class="btn btn-sm btn-secondary" onclick="javascript:editClassification(0,#local.adminSiteResourceID#);">
				<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
				<span class="btn-wrapper--label">Add Group Set</span>
			</button>
		</div>
	</div>
	<div class="card-body pb-3">
		<table id="linkedClassificationsTable" class="table table-sm table-striped table-bordered" style="width:100%">
			<thead>
				<tr>
					<th id="columnid"></th>
					<th>Group Set</th>
					<th>Actions</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
<div id="featuredGroups_msg" class="alert alert-info mt-3">Management of Featured Groups has moved to <a href="#local.manageGroupsLink#">Manage Groups</a>.</div>
</cfoutput>
</form>