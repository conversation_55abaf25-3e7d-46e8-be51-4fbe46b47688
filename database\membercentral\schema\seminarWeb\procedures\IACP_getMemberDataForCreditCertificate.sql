ALTER PROC dbo.IACP_getMemberDataForCreditCertificate
@enrollmentID int,
@programType varchar(5)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @depomemberdataID int;
	DECLARE @orgsToCheck TABLE (orgcode varchar(10) PRIMARY KEY);
	DECLARE @memberIDs TABLE (memberID int PRIMARY KEY, orgID int);

	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_IACP') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_IACP;
	IF OBJECT_ID('tempdb..#tmpMAResults_IACP') IS NOT NULL
		DROP TABLE #tmpMAResults_IACP;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_TXRX') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_TXRX;
	IF OBJECT_ID('tempdb..#tmpMAResults_TXRX') IS NOT NULL
		DROP TABLE #tmpMAResults_TXRX;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_WSPA') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_WSPA;
	IF OBJECT_ID('tempdb..#tmpMAResults_WSPA') IS NOT NULL
		DROP TABLE #tmpMAResults_WSPA;
	CREATE TABLE #tmpMAMemberIDs_IACP (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults_IACP (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpMAMemberIDs_TXRX (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults_TXRX (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpMAMemberIDs_WSPA (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults_WSPA (MCAutoID int IDENTITY(1,1) NOT NULL);

	INSERT INTO @orgsToCheck (orgcode) VALUES ('IACP'),('TXRX'),('WSPA');

	IF @programType = 'SWL' BEGIN
		SELECT @depomemberdataID = d.depomemberdataID
		FROM dbo.tblEnrollments AS e
		INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
			AND e.enrollmentID = @enrollmentID
		INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID
		INNER JOIN dbo.tblSeminars AS sem ON sem.seminarID = e.seminarID 
		INNER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = sem.seminarID
		INNER JOIN dbo.tblUsers AS u ON u.userid = e.userid
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataid = u.depomemberdataid;
	END
	ELSE BEGIN
		SELECT @depomemberdataID = d.depomemberdataID
		FROM dbo.tblenrollments AS e
		INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
			AND e.enrollmentID = @enrollmentID
		INNER JOIN dbo.tblUsers AS u ON u.userid = e.userid
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataid = u.depomemberdataid;
	END

	-- get active linked memberIDs
	INSERT INTO @memberIDs (memberID, orgID)
	SELECT m.memberID, m.orgID
	FROM membercentral.dbo.ams_networkProfiles AS np 
	INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp ON np.profileID = mnp.profileID
		AND mnp.[status] = 'A'
		AND np.depomemberdataID = @depomemberdataID
		AND np.[status] = 'A'
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = mnp.memberID
		AND m.memberID = m.activeMemberID
		AND m.[status] in ('A','I')
	INNER JOIN membercentral.dbo.organizations AS o ON o.orgID = m.orgID
	INNER JOIN @orgsToCheck AS otc ON otc.orgcode = o.orgcode;

	-- get active memberIDs when depoaccount is in waiting
	INSERT INTO @memberIDs (memberID, orgID)
	SELECT m.memberID, m.orgID
	FROM trialsmith.dbo.depomemberdata d
	INNER JOIN membercentral.dbo.ams_members AS mAll ON mAll.memberID = d.MCmemberIDtemp
		AND d.depomemberdataID = @depomemberdataID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = mAll.activeMemberID
		AND m.[status] in ('A','I')
	INNER JOIN membercentral.dbo.organizations AS o ON o.orgID = m.orgID
	INNER JOIN @orgsToCheck AS otc ON otc.orgcode = o.orgcode
	LEFT OUTER JOIN @memberIDs as tempM on tempM.memberID = m.memberID
	WHERE tempM.memberID IS NULL;

	INSERT INTO #tmpMAMemberIDs_IACP (memberID)
	SELECT DISTINCT memberID FROM @memberIDs;
	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('IACP');
	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='dateOfBirth,nabpNumber',
		@membersTableName='#tmpMAMemberIDs_IACP', @membersResultTableName='#tmpMAResults_IACP';

	INSERT INTO #tmpMAMemberIDs_TXRX (memberID)
	SELECT DISTINCT memberID FROM @memberIDs;
	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('TXRX');
	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='dateOfBirth,nabpNumber',
		@membersTableName='#tmpMAMemberIDs_TXRX', @membersResultTableName='#tmpMAResults_TXRX';

	INSERT INTO #tmpMAMemberIDs_WSPA (memberID)
	SELECT DISTINCT memberID FROM @memberIDs;
	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('WSPA');
	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='dateOfBirth,nabpNumber',
		@membersTableName='#tmpMAMemberIDs_WSPA', @membersResultTableName='#tmpMAResults_WSPA';

	SELECT TOP 1 right('0' + rtrim(month(coalesce(mdIACP.dateOfBirth, mdTXRX.dateOfBirth, mdWSPA.dateOfBirth))),2) + '/' 
			+ right('0' + rtrim(day(coalesce(mdIACP.dateOfBirth, mdTXRX.dateOfBirth, mdWSPA.dateOfBirth))),2) AS DOB,
		coalesce(mdIACP.nabpNumber, mdTXRX.nabpNumber, mdWSPA.nabpNumber) AS NABPNumber
	FROM @memberIDs as m
	INNER JOIN membercentral.dbo.organizations o ON o.orgID = m.orgID
	LEFT OUTER JOIN #tmpMAResults_IACP AS mdIACP ON mdIACP.memberID = m.memberID
	LEFT OUTER JOIN #tmpMAResults_TXRX AS mdTXRX ON mdTXRX.memberID = m.memberID
	LEFT OUTER JOIN #tmpMAResults_WSPA AS mdWSPA ON mdWSPA.memberID = m.memberID;

	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_IACP') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_IACP;
	IF OBJECT_ID('tempdb..#tmpMAResults_IACP') IS NOT NULL
		DROP TABLE #tmpMAResults_IACP;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_TXRX') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_TXRX;
	IF OBJECT_ID('tempdb..#tmpMAResults_TXRX') IS NOT NULL
		DROP TABLE #tmpMAResults_TXRX;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_WSPA') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_WSPA;
	IF OBJECT_ID('tempdb..#tmpMAResults_WSPA') IS NOT NULL
		DROP TABLE #tmpMAResults_WSPA;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
