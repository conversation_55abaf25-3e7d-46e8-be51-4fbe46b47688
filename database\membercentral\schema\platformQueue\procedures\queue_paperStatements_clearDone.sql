ALTER PROC dbo.queue_paperStatements_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusDone int;
	EXEC dbo.queue_getStatusIDbyType @queueType='PaperStatements', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;

	select distinct qid.itemGroupUID
	into #tmpitemGroupUIDs
	from platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	where qi.queueStatusID = @statusDone
		except
	select distinct qid.itemGroupUID
	from platformQueue.dbo.tblQueueItems as qi
	inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	where qi.queueStatusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0 GOTO on_done;

	BEGIN TRAN;
		DELETE from platformQueue.dbo.tblQueueItems
		where itemUID in (
			select qi.itemUID
			FROM platformQueue.dbo.tblQueueItems as qi
			inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
			INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qid.itemGroupUID
			WHERE qi.queueStatusID = @statusDone
		);

		DELETE from platformQueue.dbo.tblQueueItemData
		where itemUID not in (select itemUID from platformQueue.dbo.tblQueueItems);
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
