ALTER PROC dbo.general_PharmacySWLEnrollments
@participantID int,
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int;

	-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
	SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0);
	SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0);

	IF OBJECT_ID('tempdb..#tmpEntries') IS NOT NULL
		DROP TABLE #tmpEntries;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_IACP') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_IACP;
	IF OBJECT_ID('tempdb..#tmpMAResults_IACP') IS NOT NULL
		DROP TABLE #tmpMAResults_IACP;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_PPAG') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_PPAG;
	IF OBJECT_ID('tempdb..#tmpMAResults_PPAG') IS NOT NULL
		DROP TABLE #tmpMAResults_PPAG;
	IF OBJECT_ID('tempdb..##tmpSWLEnrollments') IS NOT NULL
		DROP TABLE ##tmpSWLEnrollments;
	CREATE TABLE #tmpMAMemberIDs_IACP (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults_IACP (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpMAMemberIDs_PPAG (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults_PPAG (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpEntries (autoID int IDENTITY(1,1), enrollmentID int, signedUpOrgCode varchar(10), seminarName varchar(250),
		depoMemberDataID int, memberID int, isTempMemberID bit);

	INSERT INTO #tmpEntries (enrollmentID, signedUpOrgCode, seminarName, depoMemberDataID, memberID, isTempMemberID)
	SELECT e.enrollmentID, p.orgcode, s.seminarName, d.depoMemberDataID, m.memberID, 0 AS isTempMemberID
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = e.participantID
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID
	INNER JOIN dbo.tblUsers AS u ON u.userID = e.userID
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON sswl.seminarID = e.seminarID
	INNER JOIN dbo.tblSeminars as s on s.seminarID = sswl.seminarID
		AND s.participantID = @participantID
	INNER JOIN membercentral.dbo.ams_networkProfiles np ON d.depomemberdataID = np.depomemberdataID
	INNER JOIN membercentral.dbo.ams_memberNetworkProfiles mnp ON np.profileID = mnp.profileID
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = mnp.memberID
	WHERE e.dateEnrolled > @startdate
	AND e.dateEnrolled < @enddate
	AND e.isActive = 1
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
		UNION
	SELECT e.enrollmentID, p.orgcode, s.seminarName, d.depoMemberDataID, m.memberID, 1 AS isTempMemberID
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = e.participantID
	INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWLive AS sswl ON e.seminarID = sswl.seminarID
	INNER JOIN dbo.tblSeminars as s on sswl.seminarID = s.seminarID 
		AND s.participantID = @participantID
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = d.MCmemberIDtemp
	WHERE e.dateEnrolled > @startdate
	AND e.dateEnrolled < @enddate
	AND e.isActive = 1
	AND (d.adminflag2 is null or d.adminflag2 <> 'Y');

	INSERT INTO #tmpMAMemberIDs_IACP (memberID)
	SELECT DISTINCT tmp.memberID
	FROM #tmpEntries AS tmp
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = tmp.memberID
		AND m.[status] <> 'D';

	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('IACP');
	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='nabpNumber,dateOfBirth',
		@membersTableName='#tmpMAMemberIDs_IACP', @membersResultTableName='#tmpMAResults_IACP';

	INSERT INTO #tmpMAMemberIDs_PPAG (memberID)
	SELECT DISTINCT tmp.memberID
	FROM #tmpEntries AS tmp
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = tmp.memberID
		AND m.[status] <> 'D'
	WHERE tmp.isTempMemberID = 0;

	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('PPAG');
	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='NABP Number,Date of Birth',
		@membersTableName='#tmpMAMemberIDs_PPAG', @membersResultTableName='#tmpMAResults_PPAG';

	-- put data into temp table 
	;with rawdata as (
		SELECT TOP 100 PERCENT d.depoMemberDataID as depoMemberDataID
			, tmp.seminarName as [Title]
			, tmp.signedUpOrgCode as [SignedUpOn]
			, CASE WHEN coalesce(IACPmd.memberID, PPAGmd.memberID) is NULL THEN 'No' ELSE 'Yes' END as [Member]
			, coalesce(IACPmd.memberID, PPAGmd.memberID) as [MemberID]
			, d.FirstName AS [FirstName]
			, d.LastName AS [LastName]
			, d.billingfirm AS [Pharmacy]
			, d.billingaddress AS [Address]
			, d.billingaddress2 AS [Address2]
			, d.billingcity AS [City]
			, d.billingstate AS [State]
			, d.billingzip AS [Zip]
			, d.phone AS [Phone]
			, d.email AS [Email]
			, CASE WHEN coalesce(IACPmd.nabpNumber, PPAGmd.[NABP Number]) IS NULL THEN eac.idNumber
				ELSE coalesce(IACPmd.nabpNumber, PPAGmd.[NABP Number]) END AS nabpNumber
			, right('0' + rtrim(month(coalesce(IACPmd.dateOfBirth, PPAGmd.[Date of Birth]))),2) + right('0' + rtrim(day(coalesce(IACPmd.dateOfBirth, PPAGmd.[Date of Birth]))),2) AS [BirthDate]
			, convert(varchar(10),e.dateEnrolled,101) as [DateEnrolled]
			, convert(varchar(10),e.dateCompleted,101) as [DateCompleted]
			, eswl.attended AS [Attended]
			, eswl.joinTime AS [JoinTime]
			, eswl.exitTime AS [ExitTime]
			, eswl.duration AS [Duration]
		FROM #tmpEntries AS tmp
		INNER JOIN dbo.tblEnrollments AS e ON e.enrollmentID = tmp.enrollmentID
		INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON eswl.enrollmentID = e.enrollmentID
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = tmp.depoMemberDataID
		LEFT OUTER JOIN #tmpMAResults_IACP AS IACPmd ON IACPmd.memberID = tmp.memberID
		LEFT OUTER JOIN #tmpMAResults_PPAG AS PPAGmd ON PPAGmd.memberID = tmp.memberID
		LEFT OUTER JOIN tblEnrollmentsAndCredit AS eac ON eac.enrollmentID = e.enrollmentID
		WHERE tmp.isTempMemberID = 0

		UNION 

		SELECT TOP 100 PERCENT d.depoMemberDataID as depoMemberDataID
			, tmp.seminarName as [Title]
			, tmp.signedUpOrgCode as [SignedUpOn]
			, CASE WHEN IACPmd.memberID is NULL THEN 'No' ELSE 'Yes' END as [Member]
			, CASE WHEN IACPmd.memberID is NULL THEN NULL ELSE IACPmd.memberID END as [MemberID]
			, d.FirstName AS [FirstName]
			, d.LastName AS [LastName]
			, d.billingfirm AS [Pharmacy]
			, d.billingaddress AS [Address]
			, d.billingaddress2 AS [Address2]
			, d.billingcity AS [City]
			, d.billingstate AS [State]
			, d.billingzip AS [Zip]
			, d.phone AS [Phone]
			, d.email AS [Email]
			, CASE WHEN IACPmd.nabpNumber IS NULL THEN eac.idNumber ELSE IACPmd.nabpNumber END AS nabpNumber
			, right('0' + rtrim(month(IACPmd.dateOfBirth)),2) + right('0' + rtrim(day(IACPmd.dateOfBirth)),2) AS [BirthDate]
			, convert(varchar(10),e.dateEnrolled,101) as [DateEnrolled]
			, convert(varchar(10),e.dateCompleted,101) as [DateCompleted]
			, eswl.attended AS [Attended]
			, eswl.joinTime AS [JoinTime]
			, eswl.exitTime AS [ExitTime]
			, eswl.duration AS [Duration]
		FROM #tmpEntries AS tmp
		INNER JOIN dbo.tblEnrollments AS e ON e.enrollmentID = tmp.enrollmentID
		INNER JOIN dbo.tblEnrollmentsSWLive AS eswl ON e.enrollmentID = eswl.enrollmentID 
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = tmp.depoMemberDataID
		LEFT OUTER JOIN #tmpMAResults_IACP AS IACPmd ON IACPmd.memberID = tmp.memberID
		LEFT OUTER JOIN tblEnrollmentsAndCredit AS eac ON eac.enrollmentID = tmp.enrollmentID
		WHERE tmp.isTempMemberID = 1
	)
	SELECT depoMemberDataID as depoMemberDataID
		, dbo.fn_csvSafeString(title) as [Title]
		, dbo.fn_csvSafeString(signedUpOn) as [SignedUpOn]
		, dbo.fn_csvSafeString(Member) as [Member]
		, dbo.fn_csvSafeString(MemberID) as [MemberID]
		, dbo.fn_csvSafeString(firstName) as [First Name]
		, dbo.fn_csvSafeString(lastName) as [Last Name]
		, dbo.fn_csvSafeString(Pharmacy) as [Pharmacy/Company]
		, dbo.fn_csvSafeString(Address) as [Address]
		, dbo.fn_csvSafeString(Address2) as [Address2]
		, dbo.fn_csvSafeString(City) as [City]
		, dbo.fn_csvSafeString(State) as [State]
		, dbo.fn_csvSafeString(Zip) as [Zip]
		, dbo.fn_csvSafeString(phone) as [Phone]
		, dbo.fn_csvSafeString(email) as [Email]
		, dbo.fn_csvSafeString(NABPNumber) as [NABP Number]
		, dbo.fn_csvSafeString(BirthDate) as [Birth Date]
		, dbo.fn_csvSafeString(dateEnrolled) as [Date Enrolled]
		, dbo.fn_csvSafeString(dateCompleted) as [Date Completed]
		, dbo.fn_csvSafeString(attended) AS [Attended]
		, dbo.fn_csvSafeString(joinTime) AS [Join Time]
		, dbo.fn_csvSafeString(exitTime) AS [Exit Time]
		, dbo.fn_csvSafeString(duration) AS [Duration]
	INTO ##tmpSWLEnrollments
	FROM rawdata
	ORDER BY [Title], [Last Name], [First Name], [Email], [Date Enrolled];

	-- export data
	DECLARE @cmd varchar(6000);
	DECLARE @tmpBCP TABLE (theoutput varchar(max));
	set @cmd = 'bcp ##tmpSWLEnrollments out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(40));
	insert into @tmpBCP (theoutput)
	exec master..xp_cmdshell @cmd;

	-- return count of records
	SELECT count(*) AS returnCount
	FROM ##tmpSWLEnrollments;

	-- get fields returned
	EXEC tempdb.dbo.SP_COLUMNS ##tmpSWLEnrollments;

	IF OBJECT_ID('tempdb..#tmpEntries') IS NOT NULL
		DROP TABLE #tmpEntries;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_IACP') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_IACP;
	IF OBJECT_ID('tempdb..#tmpMAResults_IACP') IS NOT NULL
		DROP TABLE #tmpMAResults_IACP;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_PPAG') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_PPAG;
	IF OBJECT_ID('tempdb..#tmpMAResults_PPAG') IS NOT NULL
		DROP TABLE #tmpMAResults_PPAG;
	IF OBJECT_ID('tempdb..##tmpSWLEnrollments') IS NOT NULL
		DROP TABLE ##tmpSWLEnrollments;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
