<cfsavecontent variable="local.matchSettingsJS">
	<cfoutput>
	<script language="javascript">
		function classificationsTableInit(){
			classificationsTable = $('##classificationsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paginate": false,
				"info": false,
				"language": {
					"lengthMenu": "_MENU_",
					"emptyTable": "No Classifications Found"
				},
				"ajax": { 
					"url": "#local.link.classificationList#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [ 
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? ($.trim(data.name).length ? data.name : data.groupsetname) : data;
						},
						"orderable": false
					},
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 m-1" title="Edit Group Set" onclick="editClassification('+data.classificationid+');return false;"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-danger p-1 m-1" id="btnDel'+data.classificationid+'" onclick="removeClassification('+data.classificationid+');return false;" title="Delete Group Set"><i class="fa-solid fa-trash-can"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"orderable": false,
						"className": "text-center",
						"width": "15%"
					}
				],
				"searching": false,
				"ordering": false
			});
		}
		function editClassification(cID,srID){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: (cID > 0 ? 'Edit' : 'Add') + ' Classification' ,
				iframe: true,
				contenturl: '#local.link.editClassification#&classificationID=' + cID + '&siteResourceID=' + srID,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSaveClassification").click',
					extrabuttonlabel: 'Save Details'
				}
			});
		}
		function refreshGrids() {
			classificationsTable.draw();
		}
		function reloadClassifications() { classificationsTable.draw(); }
		function removeClassification(cid) {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){ reloadClassifications(); }
				else {
					alert('We were unable to remove this classification. Try again.');
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
				}
			};

			let delBtn = $('##btnDel'+cid);
			mca_initConfirmButton(delBtn, function(){
				var objParams = { classificationID:cid };
				TS_AJX('ADMMEMBERSETTINGS','deleteClassification',objParams,removeResult,removeResult,10000,removeResult);
			});
		}
		/* functions for field set selector widget */
		function getAvailableAndSelectedFieldSetsForParticipant(onCompleteFunc){
			let objParams = { siteResourceID:#local.siteResourceID#, area:'matchSettings' };
			TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		function createMemberFieldUsageForParticipant(fsID, onCompleteFunc) {
			let objParams = { fieldSetID:fsID, siteResourceID:#local.siteResourceID#, area:'matchSettings' };
			TS_AJX('FIELDSETWIDGET','createMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
		function removeParticipantFieldUsage(useID, onCompleteFunc) {
			var objParams = { useID:useID };
			TS_AJX('FIELDSETWIDGET','fsRemove',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}

		$(function() {
			classificationsTableInit();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.matchSettingsJS)#">

<cfoutput>
<h4>Match Settings</h4>
<h5 class="mt-3">Participant Classifications</h5>
<div class="toolButtonBar pb-0">
	<div><a href="javascript:editClassification(0,#local.networkAdminSiteResourceID#);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="" data-original-title="Click to add a Group Set.">
		<i class="fa-solid fa-circle-plus"></i> Add Group Set</a>
	</div>
</div>
<table id="classificationsTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Group Set</th>
			<th class="text-center">Actions</th>
		</tr>
	</thead>
</table>

<h5 class="mt-4">Participant Field Set</h5>
<div class="mb-2">Use these Member Field Sets for the "Match Tool" filter:</div>
#local.strParticipantFieldsSelector.html#
</cfoutput>