<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="getMemberDirectories" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"memberDirectoryName")>
		<cfset arrayAppend(local.arrCols,"memberCount")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfset local.AppearInDirectoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="members", functionName="AppearInDirectory")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberDirectory">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpMemberDirectory') IS NOT NULL
				DROP TABLE ##tmpMemberDirectory;
			CREATE TABLE ##tmpMemberDirectory (memberDirectoryID int PRIMARY KEY, memberDirectoryName varchar(300), rightsXML xml, memberCount int, siteResourceID int, row int);

			DECLARE @RTID int, @siteID int, @orgID int, @loggedInMemberID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300), @functionID int;

			SELECT @RTID = dbo.fn_getResourceTypeID('Community');
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			SET @loggedInMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;
			SET @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AppearInDirectoryRFID#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			INSERT INTO ##tmpMemberDirectory (memberDirectoryID, memberDirectoryName, rightsXML, membercount, siteResourceID, row)
			SELECT memberDirectoryID, memberDirectoryName, rightsXML, membercount, siteResourceID, ROW_NUMBER() OVER (ORDER BY #local.orderby#)
			FROM (
				SELECT md.memberDirectoryID, 
					memberDirectoryName = ai.applicationInstanceName + CASE WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END,
					dbo.fn_cache_perms_getResourceRightsXML(ai.siteResourceID,@loggedInMemberID,@siteID) as rightsXML, ai.siteResourceID,
					membercount = ( 
						SELECT count(m.memberID)
						FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
							AND gprp.rightPrintID = srfrp.rightPrintID
						INNER JOIN dbo.ams_members AS m on m.orgID = @orgID
							AND m.groupPrintID = gprp.groupPrintID 
							AND m.memberID = m.activeMemberID
						WHERE srfrp.siteID = @siteID
						AND srfrp.siteResourceID = sr.siteResourceID
						AND srfrp.functionID = @functionID
					)
				FROM dbo.ams_memberDirectories md
				INNER JOIN dbo.cms_applicationInstances ai ON ai.applicationInstanceID = md.applicationInstanceID 
					AND ai.siteID = @siteID
				INNER JOIN dbo.cms_siteResources sr ON sr.siteID = @siteID AND ai.siteResourceID = sr.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteID = @siteID AND parentResource.siteResourceID = sr.parentSiteResourceID
				LEFT OUTER JOIN dbo.cms_siteResources AS grandparentResource
					INNER JOIN dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
					ON grandparentResource.siteID = @siteID 
					AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @RTID
			) AS tmp
			WHERE rightsXML.exist('(/rights/right[@allowed="1"])[1]') = 1
			<cfif len(local.searchValue)>
				 AND tmp.memberDirectoryName LIKE @searchValue
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT memberDirectoryID, memberDirectoryName, rightsXML, @totalCount AS totalcount, membercount, siteResourceID
			FROM ##tmpMemberDirectory as tmp
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpMemberDirectory') IS NOT NULL
				DROP TABLE ##tmpMemberDirectory;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryMemberDirectory">
			<cfset local.arrData.append({
				"memberDirectoryID": local.qryMemberDirectory.memberDirectoryID,
				"memberDirectoryName": local.qryMemberDirectory.memberDirectoryName,
				"memberDirectoryNameEncoded": encodeForHTMLAttribute(local.qryMemberDirectory.memberDirectoryName),
				"memberCount": local.qryMemberDirectory.memberCount,
				"siteResourceID":local.qryMemberDirectory.siteResourceID,
				"canedit":XMLSearch(local.qryMemberDirectory.rightsXML,"string(//right[@functionName='Edit']/@allowed)") IS 1,
				"candelete": XMLSearch(local.qryMemberDirectory.rightsXML,"string(//right[@functionName='Delete']/@allowed)") IS 1,
				"DT_RowId": "mdrow_#local.qryMemberDirectory.memberDirectoryID#"
			})>
		</cfloop>
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryMemberDirectory.totalcount),
			"recordsFiltered":  val(local.qryMemberDirectory.totalcount),
			"data": local.arrData
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getCurrentSortOptionsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qrySorts" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT s.memberDirectorySortID, s.memberDirectoryID, s.sortDefaultID, s.userGroupId, s.AscOrDesc,
				d.description AS memberDesc, g.groupPathExpanded AS groupDesc, s.sortOrder
			FROM dbo.ams_memberDirectorySorts as s
			INNER JOIN dbo.ams_memberDirectories as md ON s.memberDirectoryID = md.memberDirectoryID
				AND md.memberDirectoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mdid',0)#">
			INNER JOIN dbo.cms_applicationInstances as ai ON md.applicationInstanceID = ai.applicationInstanceID
				AND ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			INNER JOIN dbo.cms_siteResources as sr ON ai.siteResourceID = sr.siteResourceID
				AND sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			LEFT OUTER JOIN dbo.ams_groups as g ON s.userGroupId = g.groupID
				AND g.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
				AND g.status <> 'D' 
				AND g.hideOnGroupLists = 0 
			LEFT OUTER JOIN dbo.ams_memberDirectorySortDefaults as d ON s.sortDefaultID = d.sortDefaultID
			ORDER BY s.sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qrySorts">
			<cfset local.arrData.append({
				"currentrow": local.qrySorts.currentRow,
				"memberdirectorysortid": local.qrySorts.memberDirectorySortID,
				"usergroupid": local.qrySorts.userGroupId,
				"groupdesc": local.qrySorts.groupDesc,
				"memberdesc": local.qrySorts.memberDesc,
				"displaydesc": isNumeric(local.qrySorts.userGroupId) ? local.qrySorts.groupDesc : local.qrySorts.memberDesc,
				"orderdir": local.qrySorts.AscOrDesc,
				"switchtodir": local.qrySorts.AscOrDesc eq 'ASC' ? 'DESC' : 'ASC',
				"recordcount": local.qrySorts.recordCount,
				"DT_RowId": "curr_so_#local.qrySorts.memberDirectorySortID#"
			})>
		</cfloop>
		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": local.qrySorts.recordCount,
			"recordsFiltered": local.qrySorts.recordCount,
			"data": local.arrData
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getAvailableSortOptionsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qrySorts" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">,
				@memberDirectoryID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mdid',0)#">;

			SELECT 1 as otype, sd.sortDefaultID as typeID, sd.description as typeDesc
			FROM dbo.ams_memberDirectorySortDefaults sd
			WHERE sd.sortDefaultID NOT IN (
				SELECT isnull(mds.sortDefaultID,0)
				FROM dbo.ams_memberDirectorySorts mds
				INNER JOIN dbo.ams_memberDirectories md ON mds.memberDirectoryID = md.memberDirectoryID
				INNER JOIN dbo.cms_applicationInstances ai ON md.applicationInstanceID = ai.applicationInstanceID
				INNER JOIN dbo.cms_siteResources sr ON ai.siteResourceID = sr.siteResourceID
				WHERE sr.siteID = @siteID
				AND md.memberDirectoryID = @memberDirectoryID
			)
				UNION ALL
			SELECT 2 as otype, g.groupID as typeID, 'Group: ' + g.groupPathExpanded as typeDesc
			FROM dbo.ams_groups g
			WHERE g.isSystemGroup = 0
			AND g.orgID = @orgID 
			AND g.status <> 'D'   
			AND g.hideOnGroupLists = 0 
			AND g.groupID NOT IN (
				SELECT isnull(mds.userGroupID,0)
				FROM dbo.ams_memberDirectorySorts mds
				INNER JOIN dbo.ams_memberDirectories md ON mds.memberDirectoryID = md.memberDirectoryID
				INNER JOIN dbo.cms_applicationInstances ai ON md.applicationInstanceID = ai.applicationInstanceID
				INNER JOIN dbo.cms_siteResources sr ON ai.siteResourceID = sr.siteResourceID
				WHERE sr.siteID = @siteID
				AND md.memberDirectoryID = @memberDirectoryID
			)
			ORDER BY otype, typeDesc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qrySorts">
			<cfset local.arrData.append({
				"currentrow": local.qrySorts.currentRow,
				"otype": local.qrySorts.otype,
				"typeid": local.qrySorts.typeID,
				"typedesc": local.qrySorts.typeDesc,
				"recordcount": local.qrySorts.recordCount,
				"DT_RowId": "avail_so_#local.qrySorts.currentRow#"
			})>
		</cfloop>
		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": local.qrySorts.recordCount,
			"recordsFiltered": local.qrySorts.recordCount,
			"data": local.arrData
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getClassifications" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryClassifications" >
			SELECT c.classificationID, coalesce(c.name,gs.groupSetName) as classificationName,
				g.groupID, g.groupPathExpanded AS thePathExpanded, coalesce(gsg.labelOverride,g.groupName) as groupName,
				count(gsg.groupID) OVER(PARTITION BY c.classificationID) as grpCount, c.classificationOrder
			FROM dbo.ams_memberDirectories as md
			INNER JOIN dbo.ams_memberDirectoryClassifications as c ON c.memberDirectoryID = md.memberDirectoryID
			INNER JOIN dbo.cms_applicationInstances as ai ON md.applicationInstanceID = ai.applicationInstanceID
			INNER JOIN dbo.cms_siteResources as sr ON ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.ams_memberGroupSets as gs on gs.groupSetID = c.groupSetID
			LEFT OUTER JOIN dbo.ams_memberGroupSetGroups as gsg 
				INNER JOIN dbo.ams_groups as g ON g.groupID = gsg.groupID
					AND g.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID#">
					AND g.status <> 'D' 
				on gsg.groupSetID = gs.groupSetID
			WHERE md.memberDirectoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mdid',0)#">
			AND sr.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID#">
			ORDER by c.classificationOrder, classificationName, groupName
		</cfquery>

		<cfquery name="local.qryUniqueGroups" dbtype="query">
			select classificationID
			from local.qryClassifications
			group by classificationID
		</cfquery>

		<cfset local.arrData = []>
		<cfset local.index = 0>

		<cfoutput query="local.qryClassifications" group="classificationID">
			<cfset local.index = local.index + 1>
			<cfset local.rowID = "cRow-#val(local.qryClassifications.classificationID)#">

			<cfset local.arrData.append({
				"level":1,
				"classificationID": local.qryClassifications.classificationID,
				"classificationName": local.qryClassifications.classificationName,
				"grpCount": local.qryClassifications.grpCount,
				"canMoveUp": local.index neq 1,
				"canMoveDown": local.index neq local.qryUniqueGroups.recordCount and local.qryUniqueGroups.recordCount gt 1,
				"DT_RowId": local.rowID,
				"DT_RowClass": "child-of-gridRoot"
			})>

			<cfif local.qryClassifications.grpCount>
				<cfoutput>
					<cfset local.arrData.append({
						"level":2,
						"groupName": local.qryClassifications.groupName,
						"thePathExpanded": local.qryClassifications.thePathExpanded,
						"currentrow": local.qryClassifications.currentRow,
						"DT_RowId": "gRow-#local.qryClassifications.groupID#",
						"DT_RowClass": "child-of-#local.rowID# d-none"
					})>
				</cfoutput>
			</cfif>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": local.qryClassifications.recordCount,
			"recordsFiltered": local.qryClassifications.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
</cfcomponent>