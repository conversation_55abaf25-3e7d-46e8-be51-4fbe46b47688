CREATE PROC dbo.sponsors_moveSponsorGrouping
@referenceType varchar(50),
@sponsorGroupingID int,
@direction varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF @direction NOT IN ('up','down')
		RAISERROR('Invalid direction. Must be up or down.',16,1);

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		DECLARE @eventID int, @currentOrder int, @swapOrder int, @swapGroupingID int;

		SELECT @eventID = eventID, @currentOrder = sponsorGroupingOrder
		FROM dbo.ev_sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;

		IF @eventID IS NULL
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		-- Find the grouping to swap with
		IF @direction = 'up'
		BEGIN
			SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
			FROM dbo.ev_sponsorGrouping
			WHERE eventID = @eventID AND sponsorGroupingOrder < @currentOrder
			ORDER BY sponsorGroupingOrder DESC;
		END
		ELSE
		BEGIN
			SELECT TOP 1 @swapGroupingID = sponsorGroupingID, @swapOrder = sponsorGroupingOrder
			FROM dbo.ev_sponsorGrouping
			WHERE eventID = @eventID AND sponsorGroupingOrder > @currentOrder
			ORDER BY sponsorGroupingOrder ASC;
		END

		-- Perform the swap if we found a grouping to swap with
		IF @swapGroupingID IS NOT NULL
		BEGIN
			UPDATE dbo.ev_sponsorGrouping SET sponsorGroupingOrder = @swapOrder WHERE sponsorGroupingID = @sponsorGroupingID;
			UPDATE dbo.ev_sponsorGrouping SET sponsorGroupingOrder = @currentOrder WHERE sponsorGroupingID = @swapGroupingID;
		END
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		DECLARE @seminarID int, @participantID int, @swCurrentOrder int, @swSwapOrder int, @swSwapGroupingID int;

		SELECT @seminarID = seminarID, @participantID = participantID, @swCurrentOrder = sponsorGroupingOrder
		FROM seminarWeb.dbo.sw_sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;

		IF @seminarID IS NULL
			RAISERROR('Sponsor Grouping does not exist.',16,1);

		-- Find the grouping to swap with
		IF @direction = 'up'
		BEGIN
			SELECT TOP 1 @swSwapGroupingID = sponsorGroupingID, @swSwapOrder = sponsorGroupingOrder
			FROM seminarWeb.dbo.sw_sponsorGrouping
			WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder < @swCurrentOrder
			ORDER BY sponsorGroupingOrder DESC;
		END
		ELSE
		BEGIN
			SELECT TOP 1 @swSwapGroupingID = sponsorGroupingID, @swSwapOrder = sponsorGroupingOrder
			FROM seminarWeb.dbo.sw_sponsorGrouping
			WHERE seminarID = @seminarID AND participantID = @participantID AND sponsorGroupingOrder > @swCurrentOrder
			ORDER BY sponsorGroupingOrder ASC;
		END

		-- Perform the swap if we found a grouping to swap with
		IF @swSwapGroupingID IS NOT NULL
		BEGIN
			UPDATE seminarWeb.dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @swSwapOrder WHERE sponsorGroupingID = @sponsorGroupingID;
			UPDATE seminarWeb.dbo.sw_sponsorGrouping SET sponsorGroupingOrder = @swCurrentOrder WHERE sponsorGroupingID = @swSwapGroupingID;
		END
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
