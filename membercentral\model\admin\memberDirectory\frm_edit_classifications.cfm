<cfsavecontent variable="local.classJS">
	<cfoutput>
	<script language="JavaScript">
		let classificationTable;

		function reloadClassifications() { classificationTable.draw(); }
		function editClassification(cid) {
			MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: (cid !== 0) ? 'Edit Classification' : 'Add Classification',
			iframe: true,
			contenturl: '#this.link.editClassification#&mdid=#arguments.event.getValue('mdID')#&cid=' + cid,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##classificationDetails :submit").click',
				extrabuttonlabel: 'Save Details'
			}
		});
		}
		function removeClassification(cid,rowID) {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					$('tr.child-of-'+rowID).remove();
					$('##'+rowID).remove();
					resetClassificationRows();
				}
				else {
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('We were unable to delete this classification. Try again.');
				}
			};

			let delBtn = $('##btnDelClassification'+cid);

			mca_initConfirmButton(delBtn, function(){
				var objParams = { memberDirectoryID:#arguments.event.getValue('mdID')#, memberDirectorySRID:#local.siteResourceID#, classificationID:cid };
				TS_AJX('ADMMEMBERDIRECTORY','removeClassification',objParams,removeResult,removeResult,10000,removeResult);
			});
		}
		function moveClassification(cid,rowID,dir) {
			var moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					moveClassificationsGridRow(rowID,dir);
				}
			};
			var objParams = { memberDirectoryID:#arguments.event.getValue('mdID')#, classificationID:cid, dir:dir };
			TS_AJX('ADMMEMBERDIRECTORY','doMoveClassification',objParams,moveResult,moveResult,10000,moveResult);
		}
		function moveClassificationsGridRow(rowID,dir) {
			let movingRow, targetRow;

			if(dir == 'up'){
				movingRow = $('##'+rowID);
				targetRow = movingRow.closest('tr').prevAll('tr.child-of-gridRoot:first');
			}
			else {
				movingRow = $('##'+rowID).closest('tr').nextAll('tr.child-of-gridRoot:first'); /*next row will be moved to top*/
				targetRow = $('##'+rowID);
			}

			let movingRowID = movingRow.attr('id');
			movingRow.addClass('moveRow-' + movingRowID);
			$('tr.child-of-'+movingRowID).addClass('moveRow-' + movingRowID);

			let arrMoveRows = $('tr.moveRow-'+movingRowID);
			arrMoveRows.remove().insertBefore(targetRow);
			$('tr.moveRow-'+movingRowID).removeClass('moveRow-' + movingRowID);
			resetClassificationRows();
		}
		function resetClassificationRows() {
			let childRows = $('table##classificationTable tr.child-of-gridRoot');
			if (childRows.length > 1) {
				childRows.find('a.gridRowMoveUp,a.gridRowMoveDown').removeClass('invisible');
				childRows.find('a.gridRowMoveUp').first().addClass('invisible');
				childRows.find('a.gridRowMoveDown').last().addClass('invisible');
			} else {
				childRows.find('a.gridRowMoveUp,a.gridRowMoveDown').addClass('invisible');
			}
		}
		function toggleParentRow(rowID) {
			let rowToggleBtn = $('##displayLabel_'+rowID+' i.rowToggleBtn');
			rowToggleBtn.toggleClass('fa-plus-square fa-minus-square');
			let showChildren = rowToggleBtn.hasClass('fa-minus-square');
			$('tr.child-of-'+rowID).toggleClass('d-none',!showChildren);
		}
		function initClassificationsTable(){
			classificationTable = $('##classificationTable').DataTable({
				"processing": false,
				"serverSide": true,
				"paging": false,
				"bInfo" : false,
				"language": {
					"emptyTable": "No Classifications found."
				},
				"ajax": { 
					"url": "#local.classificationsListLink#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							let thisRowID = data['DT_RowId'];
							if (type === 'display') {
								if(data.level == 1) {
									let displayName = data.classificationName + ' ('+ data.grpCount +' group' + (data.grpCount != 1 ? 's' : '') + ')';
									if(data.grpCount) renderData += '<a id="displayLabel_'+thisRowID+'" href="javascript:toggleParentRow(\''+thisRowID+'\')"><i class="far fa-plus-square rowToggleBtn pr-2"></i>'+ displayName +'</a>';
									else renderData += '<span><i class="far fa-plus-square pr-2 invisible"></i>'+ displayName +'</span>';
								}
								else renderData += '<span style="padding-left:28px;" title="'+data.thePathExpanded+'">'+data.groupName+'</span>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "75%"
					},
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							let thisRowID = data['DT_RowId'];
							if (type === 'display' && data.level == 1) {
								renderData += '<a href="javascript:editClassification('+data.classificationID+')" title="Edit Classification" class="btn btn-xs text-primary p-1 mx-1"><i class="fas fa-pencil"></i></a>';
								renderData += '<a href="javascript:removeClassification('+data.classificationID+',\''+thisRowID+'\')" id="btnDelClassification'+data.classificationID+'" title="Delete Classification" class="btn btn-xs text-danger p-1 mx-1"><i class="fa-solid fa-trash-can"></i></a>';
								renderData += '<a href="javascript:moveClassification('+data.classificationID+',\''+thisRowID+'\',\'up\')" title="Move Classification Up" class="btn btn-xs text-primary p-1 mx-1 gridRowMoveUp'+(data.canMoveUp ? "" : " invisible")+'"><i class="fas fa-arrow-up"></i></a>';							
								renderData += '<a href="javascript:moveClassification('+data.classificationID+',\''+thisRowID+'\',\'down\')" title="Move Classification Down" class="btn btn-xs text-primary p-1 mx-1 gridRowMoveDown'+(data.canMoveDown ? "" : " invisible")+'"><i class="fas fa-arrow-down"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "text-center align-top",
						"width": "25%"
					}],
					"searching": false,
					"ordering": false
			});
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.classJS#">
<cfoutput>
<div class="text-right">
	<button type="button" name="btnAddClass" class="btn btn-sm btn-primary" onclick="editClassification(0);"><span class="btn-wrapper--icon">
		<i class="fa-regular fa-circle-plus"></i>
		</span> <span class="btn-wrapper--label">Add Classification</span>
	</button>
</div>
<table id="classificationTable" class="table table-sm table-bordered table-hover" >
	<thead>
		<tr>
			<th>Classification</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>
</cfoutput>