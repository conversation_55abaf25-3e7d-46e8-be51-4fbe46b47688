$(document).ready(function(){
  $(".btn-navbar").click(function(){
    $("body").toggleClass("overlay")
  });


  $('.nav-collapse > ul > li:has(ul)').addClass('dropdown');
  $('.nav-collapse > ul > li:has(ul>li>ul)').addClass('multi-ul');
  $('.nav-collapse > ul > li.dropdown > ul').addClass('dropdown-menu');
  $('.nav-collapse > ul > li.dropdown > ul > li > ul').addClass('dropdown-submenu');

  $( ".dropdown" ).append( "<span class='menu-arrow'></span>" );
  $(".menu-arrow").click(function(){
    $(this).toggleClass('open-droupdown');
    $(this).parents(".dropdown").toggleClass('open-droupdown');
    $(this).parents(".dropdown").children(".dropdown-menu").slideToggle();
  });
  $('#primaryNavigation').show();
})