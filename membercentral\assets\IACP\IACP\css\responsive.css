@charset "utf-8";
 @media only screen and (min-width:980px) {
#header.header .navbar .nav > li.dropdown:hover > .dropdown-menu { display: block; top: 41px; visibility: visible; z-index: 999; opacity: 1; margin: 0; width: auto; }
#header.header .navbar .pull-right > li > .dropdown-menu, #header.header .navbar .nav > li > .dropdown-menu { top: -277px; padding: 0; border: none; display: block; visibility: hidden; opacity: 0; z-index: -1; transition: top 0.45s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
.dropdown-submenu > .dropdown-menu { display: block !important; left: 100%; margin-left: -1px; top: -100%; opacity: 0; visibility: hidden; border-radius: 0; overflow: hidden; }
.dropdown-submenu:hover > .dropdown-menu { display: block !important; top: 0; visibility: visible; -moz-transition: all 0.3s ease 0s; -ms-transition: all 0.3s ease 0s; -o-transition: all 0.3s ease 0s; -webkit-transition: all 0.3s ease 0s; transition: all 0.3s ease 0s; opacity: 1; }
}
@media only screen and (max-width:1199px) {
#header.header .navbar .container, .container { width: 940px; }
}
@media only screen and (max-width:979px) {
.logoBox { padding: 5px 0px 15px; }
#header.header .navbar .container, .container { width: 100%; }
.navMain { float: none; height: 40px; padding: 0; text-align: center; }
#header.header .navbar-inner { position: relative; background: #0d0d0d; min-height: 48px; }
.nav > .dropdown { padding-bottom: 0; }
#header .navbar .btn-navbar .icon-bar { width: 22px; margin-bottom: 5px; background-color: rgba(13, 13, 13, 0.8)!important; height: 3px; border-radius: 0; }
#header .navbar .btn-navbar .icon-bar:last-child { margin-bottom: 0; }
.dropdown-menu { width: 100%; }
#header.header .nav-collapse { float: none; padding: 0; width: 100%; z-index: 99; background: #0d0d0d; overflow: visible; }
#header.header .nav-collapse li { display: block; width: 100%; }
#header.header .navbar .nav li a, #header.header .navbar .nav li .dropdown-menu > li:last-child a { border: none; margin: 0; background: none; }
#header.header .navbar .nav li .dropdown-menu > li > a { padding: 15px 15px; font-size: 13px; }
#header.header .navbar .btn-navbar { margin: 0; position: absolute; top: 8px; border: none; -moz-border-radius: 0px; -ms-border-radius: 0px; -o-border-radius: 0px; -webkit-border-radius: 0px; border-radius: 0px; box-shadow: none; line-height: 1.42857; z-index: 9999; right: 15px; left: auto; padding: 6px 6px; background-color: rgba(255, 255, 255, 0.8); border: 0; border-radius: 0; background-image: none; }
#header.header .navbar .btn-navbar:hover, #header.header .navbar .btn-navbar:focus, #header.header .navbar .btn-navbar:active, #header.header .navbar .btn-navbar.active, #header.header .navbar .btn-navbar.disabled, #header.header .navbar .btn-navbar[disabled], #header.header .navbar .btn-navbar:hover, #header.header .navbar .btn-navbar:focus, #header.header .navbar .btn-navbar:active, #header.header .navbar .btn-navbar.active, #header.header .navbar .btn-navbar.disabled, #header.header .navbar .btn-navbar[disabled], #header.header .navbar .nav li .dropdown-menu > li > a:hover, #header.header .navbar .nav li .dropdown-menu > li:hover a { color: rgb(204, 204, 204); }
#header.header .navbar .nav li a, #header.header .navbar .nav li .dropdown-menu > li > a { text-align: center; color: #fff; border-radius: 0; }
#header.header .navbar .nav li a, #header.header .navbar .nav li a, #header.header .navbar .nav li .dropdown-menu > li > a { padding: 13px 15px; border: none; text-align: left; font-size: 1.3em; font-weight: normal; letter-spacing: 1px; }
#header.header .navbar .nav li .dropdown-menu > li > a:hover { border: none; }
#header.header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a:hover, #header.header .navbar .nav li a:hover, #header.header .navbar .nav li a:focus, .navbar .nav li.dropdown.open > .dropdown-toggle, .navbar .nav li.dropdown.active > .dropdown-toggle, .navbar .nav li.dropdown.open.active > .dropdown-toggle, .dropdown:hover .dropdown-toggle { -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; color: #9a0203!important; }
.dropdown-menu { margin-left: 0!important; }
#header.header .nav-collapse li .menu-arrow:after { content: ""; font-family: 'FontAwesome'; position: absolute; right: 20px; top: 13px; color: #fff; font-size: 36px; z-index: 99999; font-weight: bold; }
#header.header .nav-collapse li .menu-arrow { cursor: pointer; width: 15px; height: 15px; }
#header.header .nav-collapse li.dropdown:hover:after, #header.header .nav-collapse li.dropdown.open::after { color: #9a0203; }
#header.header .nav-collapse .nav { margin: 0; overflow-y: visible; padding-bottom: 10px; }
#header .navbar .btn-navbar .icon-bar { transition: all ease-in-out 0.3s; }
.navMain { box-sizing: border-box; display: block; height: 100%; left: 0; max-height: 0; opacity: 0; overflow-x: hidden; overflow-y: auto; position: fixed; top: 118px; -moz-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; -ms-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; -o-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; -webkit-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s; width: 100%; z-index: 14; }
body.overlay .navMain { max-height: 100vh; opacity: 1; }
body.overlay { overflow: hidden; }
#header.header .navbar .nav li.dropdown ul.dropdown-menu { background-color: rgba(0,0,0,0.8); padding: 6px 1px; position: absolute; z-index: 999999; text-align: left; }
}
 @media only screen and (max-width:767px) {
body { padding: 0px; }
.xsHidden { display: none !important; }
.xsVisible { display: block !important; }
.logoBox { background: #0d0d0d; padding-top: 15px; padding-right: 50px; }
.logoBox .brand img.xsHidden { display: none !important; }
.logoBox .brand img.xsVisible { display: inline-block !important; max-width: 46px; }
#header.header .navbar-inner { min-height: auto; }
#header.header .navbar .btn-navbar { top: -57px; }
.navMain { top: 80px; }
#header.header .navbar .nav li.dropdown.multi-ul ul { min-width: 100%; }
#header.header .navbar .nav li.dropdown.multi-ul > ul > li > ul li > a { white-space: normal; }
.footer{position: static;}
.footer .span3, .footer .span6 { width: 100%; }
.footer .footCol { margin-bottom: 20px; padding: 0px; }
}
 @media only screen and (max-width:480px) {
#header.header .navbar .nav li.dropdown.multi-ul ul li > ul li { width: 100%; }
}
