ALTER PROC dbo.general_exportSWODPharmacyCreditReport
@sponsorID int,
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int;

	-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
	SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0);
	SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0);

	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('IACP');

	IF OBJECT_ID('tempdb..#tmpEntries') IS NOT NULL
		DROP TABLE #tmpEntries;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMAResults') IS NOT NULL
		DROP TABLE #tmpMAResults;
	IF OBJECT_ID('tempdb..##tmpSWODCredit') IS NOT NULL
		DROP TABLE ##tmpSWODCredit;
	CREATE TABLE #tmpMAMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpEntries (autoID int IDENTITY(1,1), enrollmentID int, seminarID int,
		depomemberdataid int, enrollCreditID int, csalinkid int, orgmemberdataid int, memberID int);

	INSERT INTO #tmpEntries (enrollmentID, depomemberdataid, enrollCreditID, csalinkid, orgmemberdataid, memberID)
	SELECT DISTINCT e.enrollmentID, u.depomemberdataid, eac.enrollCreditID, csa.csalinkid, o.orgmemberdataid, m.memberID
	FROM dbo.tblenrollments as e
	INNER JOIN dbo.tblParticipants p on e.participantID = p.participantID
		AND e.participantID = 119
	INNER JOIN dbo.tblSeminars as sem on sem.seminarID = e.seminarID
	INNER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = sem.seminarID
	INNER JOIN dbo.tblenrollmentsandcredit as eac on eac.enrollmentid = e.enrollmentid
	INNER JOIN dbo.tblSeminarsAndCredit as sac on eac.seminarCreditID = sac.seminarCreditID
	INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = sac.csalinkid
	INNER JOIN dbo.tblCreditSponsors as cs on cs.sponsorid = csa.sponsorid
		AND cs.sponsorID = @sponsorID
	INNER JOIN dbo.tblUsers as u on u.userid = e.userid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = u.depomemberdataid
	LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.depomemberdataid = d.depomemberdataid
		AND o.orgcode = cs.orgcode
	LEFT OUTER JOIN membercentral.dbo.ams_networkProfiles np ON np.depomemberdataID = d.depomemberdataID
	LEFT OUTER JOIN membercentral.dbo.ams_memberNetworkProfiles mnp ON mnp.profileID = np.profileID
	LEFT OUTER JOIN membercentral.dbo.ams_members m ON m.memberID = mnp.memberID
	WHERE e.passed = 1
	AND eac.earnedCertificate = 1
	AND e.datecompleted BETWEEN @startdate AND @enddate
	AND e.isActive = 1
	AND (d.adminflag2 IS NULL OR d.adminflag2 <> 'Y');

	INSERT INTO #tmpMAMemberIDs (memberID)
	SELECT DISTINCT tmp.memberID
	FROM #tmpEntries AS tmp
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = tmp.memberID
		AND m.[status] <> 'D';

	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='dateOfBirth',
		@membersTableName='#tmpMAMemberIDs', @membersResultTableName='#tmpMAResults';

	SELECT DISTINCT
		dbo.fn_csvSafeString(sem.seminarname) as [Seminar Name], 
		dbo.fn_csvSafeString(d.lastname) as [Last Name],
		dbo.fn_csvSafeString(d.firstname) as [First Name],
		dbo.fn_csvSafeString(d.depomemberdataid) as [depoMemberDataID],
		dbo.fn_csvSafeString(p.orgcode) as [Signed Up On],
		dbo.fn_csvSafeString(isnull(o.address1,d.billingAddress)) as [Address Line 1],
		dbo.fn_csvSafeString(isnull(o.address2,d.billingAddress2)) as [Address Line 2],
		dbo.fn_csvSafeString(isnull(o.city,d.billingCity)) as City,
		dbo.fn_csvSafeString(isnull(o.[state],d.billingState)) as State,
		dbo.fn_csvSafeString(isnull(o.zipcode,d.billingZIP)) as ZIP,
		dbo.fn_csvSafeString(d.phone) as Phone,
		dbo.fn_csvSafeString(d.email) as [E-Mail],	
		dbo.fn_csvSafeString(eac.idnumber) as [NABP Number],
		dbo.fn_csvSafeString(right('0' + rtrim(month(tmpMD.dateOfBirth)),2) + right('0' + rtrim(day(tmpMD.dateOfBirth)),2)) AS [Birth Date],
		convert(varchar(10),e.dateenrolled,101) as [Date Enrolled],
		convert(varchar(10),e.datecompleted,101) as [Date Completed],
		eac.finaltimespent as [Minutes Spent in Course],
		CASE 
			WHEN csa.csalinkid = 205 THEN 'Pharmacists'
			WHEN csa.csalinkid = 207 THEN 'Technicians'
			WHEN csa.csalinkid = 197 THEN 'P/T'
			ELSE NULL
		END AS [Credit Type]
	INTO ##tmpSWODCredit
	FROM #tmpEntries AS tmp
	INNER JOIN tblenrollments as e ON e.enrollmentID = tmp.enrollmentID
	INNER JOIN dbo.tblParticipants p on p.participantID = e.participantID
	INNER JOIN tblSeminars as sem on sem.seminarID = e.seminarID
	INNER JOIN tblenrollmentsandcredit as eac on eac.enrollCreditID = tmp.enrollCreditID
	INNER JOIN tblCreditSponsorsAndAuthorities as csa on csa.csalinkid = tmp.csalinkid
	INNER JOIN trialsmith.dbo.depomemberdata as d on d.depomemberdataid = tmp.depomemberdataid
	LEFT OUTER JOIN trialsmith.dbo.orgmemberdata as o on o.orgmemberdataid = tmp.orgmemberdataid
	LEFT OUTER JOIN #tmpMAResults as tmpMD on tmpMD.memberID = tmp.memberID
	ORDER BY [Seminar Name], [Last Name], [First Name], [depoMemberDataID], [Signed Up On], [Credit Type],
		[E-Mail], [Address Line 1], [Address Line 2], City, [State], Zip, phone, [NABP Number], [Birth Date],
		[Date Enrolled], [Date Completed],[Minutes Spent in Course];

	-- export data
	DECLARE @cmd varchar(6000);
	DECLARE @tmpBCP TABLE (theoutput varchar(max));
	set @cmd = 'bcp ##tmpSWODCredit out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(40));
	insert into @tmpBCP (theoutput)
	exec master..xp_cmdshell @cmd;

	-- return count of records
	SELECT count(*) AS returnCount
	FROM ##tmpSWODCredit;

	-- get fields returned
	EXEC tempdb.dbo.SP_COLUMNS ##tmpSWODCredit;

	IF OBJECT_ID('tempdb..#tmpEntries') IS NOT NULL
		DROP TABLE #tmpEntries;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMAResults') IS NOT NULL
		DROP TABLE #tmpMAResults;
	IF OBJECT_ID('tempdb..##tmpSWODCredit') IS NOT NULL
		DROP TABLE ##tmpSWODCredit;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
