@charset "utf-8";
@media only screen and (min-width:979px) {
.header .navbar .nav li a { display: block; vertical-align: middle; }
.navbar .navMain .nav.pull-right { margin-right: 0; float: none; display: table; }
.navbar .navMain .nav > li { }
}
 @media only screen and (max-width:1199px) {
.header .navbar .container, .banner-text { width: 940px; }
}
@media only screen and (max-width:979px) {
.header .navbar .container, .banner-text { width: 724px; }
.navbar .btn-navbar:hover .icon-bar { background-color: #ccc; }
.header .navbar .nav li a { padding: 7px 15px; margin: 0; border: none; }
.logo { margin: 0 auto; text-align: center; }
.navMain { float: none; height: 40px; padding: 0; }
.header .navbar .nav > li > a { padding: 7px 15px; margin: 0; border: 1px solid #444; border-top: none; }
.header .navbar .nav li .dropdown-menu > li > a, .header .navbar .pull-right > li > .dropdown-menu, .header .navbar .nav > li > .dropdown-menu { border-top: none; background: #1f1f1f; color: #fff; padding: 6px 15px 6px 15px; }
.header .navbar .nav li:first-child a span::after { left: 0; bottom: 7px; }
.header .navbar .nav li:first-child a span { padding-left: 10px; }
.header .navbar .nav li.dropdown a::after { display: none; }
.header .navbar .pull-right > li > .dropdown-menu, .header .navbar .nav > li > .dropdown-menu { padding-left: 30px; }
.header .navbar .nav li .dropdown-menu > li > a { color: #b9b9b9; }
.header .navbar .navIcon { bottom: 4px; display: block; position: absolute; right: 0; z-index: 99; }
.navIcon .menu { color: #fff; display: inline-block; font-weight: bold; margin-top: 7px; text-transform: uppercase; }
.header .navbar .btn-navbar:hover span { background: #cccccc; }
.header .navbar-inner { position: relative; }
.header .nav-collapse li { width: 100%; display: block; }
.header .nav-collapse { padding: 0; float: none; width: 100%; top: 15px; z-index: 99; position: absolute; top: 49px; z-index: 99; left: 0; background: #1f1f1f; }
.header .navbar .pull-right > li > .dropdown-menu, .header .navbar .nav > li > .dropdown-menu { border: none; }
.header .nav-collapse li a {
background-color:rgba(0, 0, 0, 0.03)padding:10px 15px; border-bottom: 1px solid #2a2a2a; font-size: 13px; -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; }
.header.fixed-header .nav-collapse { padding: 0; }
.navbar .btn-navbar { background: rgba(0, 0, 0, 0); border: none; border-radius: 4px; -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; line-height: 1.42857; margin: 0; width: 100%; border-radius: 0; padding-top: 15px; height: 50px; background: black; }
.header .navbar-static .navbar-inner { padding-left: 0; padding-right: 0; }
.header .navbar .btn-navbar:hover, .header .navbar .btn-navbar:focus, .header .navbar .btn-navbar:active, .header .navbar .btn-navbar.active, .header .navbar .btn-navbar.disabled, .header .navbar .btn-navbar[disabled] { background: black; color: #ccc; }
.header .dropdown-menu>li>a:hover, .header .dropdown-menu>li>a:focus, .header .dropdown-submenu:hover>a, .header .dropdown-submenu:focus>a { background: rgba(0, 0, 0, 0.03); color: #dd3333; }
.header .navbar .nav li a { text-align: left; border: none; }
.header ul.dropdown-menu { margin: 0; }
.navMain { height: inherit; }
.navbar .btn-navbar .icon-bar { background: #444; }
.navbar .navMain .nav > li { width: 100%; }
.content { padding: 15px 0; }
.header .btn-navbar .icon-bar { background: #b3b3b3!important; display: block; height: 3px; margin-bottom: 6px; width: 28px; }
.header .btn-navbar .icon-bar:last-child { margin-bottom: 0; }
.navbar .btn-navbar:hover .icon-bar { background: #000; }
.nav .dropdown-menu { display: block; }
.nav-collapse.collapse { }
.header-in p { font-size: 27px; }
.navIn { padding: 0; }
}
@media only screen and (max-width:767px) {
.header .navbar .container, .header .navbar .container, .container, .banner-text { width: 100%; padding: 0 15px; }
.navbar .brand > img { width: 308px; }
.header .brand { margin-bottom: 30px; }
.header-in .header-top-cols:last-child { text-align: right; }
.header .navbar .container { padding: 0; }
}
