<cfstoredproc procedure="IACP_getMemberDataForCreditCertificate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
	<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentInfo.enrollmentID#">
	<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="SWOD">
	<cfprocresult name="local.qryGetDOB" resultset="1">
</cfstoredproc>
	
<cfoutput>
<html>
<head>
<title>Official Certificate of Attendance</title>
<style>
@font-face { font-family:Tahoma;panose-1:2 11 6 4 3 5 4 4 2 4;}
@font-face { font-family:Verdana;panose-1:2 11 6 4 3 5 4 4 2 4; }
p.<PERSON>, td.<PERSON>o<PERSON>ormal, li.<PERSON>o<PERSON>ormal, div.MsoNormal { margin:0in; margin-bottom:.0001pt;font-size:12.0pt;font-family:Verdana; }
.Section1{ size:8.5in 11.0in;padding-right:.4in; }
div.Section1{ page:Section1; }
tr { vertical-align:top; }
div.MsoNormal, td.MsoNormal { font-size:8.0pt; font-family:Verdana; }
table { page-break-inside:auto }
tr    { page-break-inside:avoid; page-break-after:auto }
div.MsoNormal, td.MsoNormal { font-size:7.2pt; font-family:Verdana; }
body { background-image: url(#local.assetsURL#swlSwooshWatermark.png);background-position:center;background-repeat: no-repeat;height:11in;width:8.5in;padding:.3in;}
</style>
</head>
<body>
<div class=Section1>
<div align=center><img src="#local.assetsURL#swlogoBW300.png" width="266" height="60"></div>
<br/>
<p class=MsoNormal align=center><b><span style='font-size:14.0pt;'>Official Certificate of Attendance</span></b></p>
<p class=MsoNormal align=center><b><span style='font-size:8.0pt;line-height:14pt;'>Please keep this copy for your records.</span></b></p>
<br/>
<p class=MsoNormal><span style='font-size:7.5pt;line-height:9pt;'><b>Registrar Verification of Program Activity:</b> 
	This is an Official Record of #UCASE(arguments.enrollmentInfo.fullname)#'s attendance in this program. 
	To determine #UCASE(arguments.enrollmentInfo.fullname)#'s actual participation in this program, please 
	refer to the "SeminarWeb Verification of Activity" below to verify participation. Questions 
	for the Registrar should be directed to SeminarWeb.
</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;'>&nbsp;</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;line-height:9pt;'><b>How to Obtain Credit:</b> 
	If you requested Continuing Education credit during registration, your CPE credits will be uploaded to the CPE Monitor System within 60 days of completion. It is the responsibility of the CPE attendee to provide their correct NABP e-Profile ID and birth date. If incorrect information is provided, the record will be rejected by CPE Monitor and the CPE credit will not be awarded. If you do not see CPE activity displayed for a session that you attended and it has been more than 60 days since you submitted the CPE to the provider, please contact your CPE provider.  
</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;'>&nbsp;</span></p>
<p class=MsoNormal><span style='font-size:7.5pt;line-height:9pt;'><b>Program Status by Continuing Education Accrediting Authority: (as of #dateformat(now(),"mmmm d, yyyy")#)</b><br/>
	#UCASE(arguments.enrollmentInfo.fullname)# may or may not qualify for credit with these authorities:<br/>
	Credit Approved: <cfif arguments.strSAC.qryApproved.recordcount><cfloop query="arguments.strSAC.qryApproved"><nobr>#arguments.strSAC.qryApproved.authoritycode#<cfif len(arguments.strSAC.qryApproved.courseApproval)> - #arguments.strSAC.qryApproved.courseApproval#</cfif><cfif len(arguments.strSAC.qryApproved.credits)> - #arguments.strSAC.qryApproved.credits#</cfif></nobr><cfif arguments.strSAC.qryApproved.currentrow neq arguments.strSAC.qryApproved.recordcount>; </cfif></cfloop><cfelse>(none)</cfif><br/>
	Credit Pending: <cfif arguments.strSAC.qryPending.recordcount><cfloop query="arguments.strSAC.qryPending">#arguments.strSAC.qryPending.authoritycode#<cfif arguments.strSAC.qryPending.currentrow neq arguments.strSAC.qryPending.recordcount>, </cfif></cfloop><cfelse>(none)</cfif><br/>
</span></p>
<br/>
<table width="100%" cellpadding="6" cellspacing="0">
<tr><td class=MsoNormal><b>Program:</b></td>
	<td class=MsoNormal><b>#arguments.enrollmentInfo.contentName#</b></td></tr>
<tr><td class=MsoNormal><b>Sponsor:</b></td>
	<td class=MsoNormal>#arguments.enrollmentInfo.signUpOrgDescription#</td></tr>
<tr><td class=MsoNormal><b>Accreditation Provider:</b></td>
	<td class=MsoNormal>American College of Apothecaries</td></tr>
<cfif arraylen(arguments.arrSpeakers)>
	<tr><td class=MsoNormal><b>Author(s):</b></td>
		<td class=MsoNormal>#arrayToList(arguments.arrSpeakers,"; ")#</td></tr>
</cfif>
<tr><td class=MsoNormal><b>Format:</b></td>
	<td class=MsoNormal>This was a self-paced, online, distance learning program. The participant may have been able to ask 
		questions of the moderators in the program. Materials may have been provided to the participant. The program was 
		completely web-based; there was NOT a live component to this self-paced program.
		<cfset local.arrcomponents = arrayNew(1)>
		<cfloop query="arguments.qrySeminarFiles">
			<cfif arguments.qrySeminarFiles.fileType eq "video">
				<cfset ArrayAppend(local.arrcomponents,"video")>
			<cfelseif arguments.qrySeminarFiles.fileType eq "audio">
				<cfset ArrayAppend(local.arrcomponents,"audio")>
			<cfelseif arguments.qrySeminarFiles.fileType eq "paper">
				<cfset ArrayAppend(local.arrcomponents,"papers and reading materials")>
			</cfif>
		</cfloop>
		The program included 
		<cfloop from="1" to="#arrayLen(local.arrcomponents)#" index="local.i">
			#local.arrcomponents[local.i]#<cfif arrayLen(local.arrcomponents) gt 2 and local.i lt arrayLen(local.arrcomponents)>,</cfif><cfif local.i is arrayLen(local.arrcomponents)-1> and </cfif>
		</cfloop>. 

		<cfset local.arrcontrols = arrayNew(1)>
		<cfif arguments.qrySeminarSettings.mustAttendMinutes gt 0>
			<cfset ArrayAppend(local.arrcontrols,"a minimum time requirement for successful completion")>
		</cfif>
		<cfif arguments.qrySeminarSettings.promptInterval gt 0>
			<cfset ArrayAppend(local.arrcontrols,"automated polling requiring the participant to be present during the program")>
		</cfif>
		<cfif arrayLen(local.arrcontrols) gt 0>
			The program included these auditing controls: 
			<cfloop from="1" to="#arrayLen(local.arrcontrols)#" index="local.i">
				#local.arrcontrols[local.i]#<cfif arrayLen(local.arrcontrols) gt 2 and local.i lt arrayLen(local.arrcontrols)>;</cfif><cfif local.i is arrayLen(local.arrcontrols)-1> and </cfif>
			</cfloop>. 
		</cfif>
	</td></tr>
<tr><td class=MsoNormal><b>SeminarWeb Verification of Activity:</b></td>
	<td class=MsoNormal>Attendance at this program is certified by SeminarWeb as follows:
		<br/><br/>
		Participant Identifier: #arguments.enrollmentInfo.sourceID#<br/>
		#UCASE(arguments.enrollmentInfo.fullname)#<br/>
		<cfif len(arguments.enrollmentInfo.billingfirm)>#UCASE(arguments.enrollmentInfo.billingfirm)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingaddress)>#UCASE(arguments.enrollmentInfo.billingaddress)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingaddress2)>#UCASE(arguments.enrollmentInfo.billingaddress2)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingaddress3)>#UCASE(arguments.enrollmentInfo.billingaddress3)#<br/></cfif>
		<cfif len(arguments.enrollmentInfo.billingcity)>#UCASE(arguments.enrollmentInfo.billingcity)#, </cfif>
		<cfif len(arguments.enrollmentInfo.billingstate)>#UCASE(arguments.enrollmentInfo.billingstate)# </cfif>
		<cfif len(arguments.enrollmentInfo.billingzip)>#UCASE(arguments.enrollmentInfo.billingzip)#</cfif>
		<br/>
		NABP e-Profile ID: <cfif isdefined("arguments.strCredit.idNumber")>#arguments.strCredit.idNumber#
			<cfelseif isdefined("local.qryGetDOB.NABPNumber")>#local.qryGetDOB.NABPNumber#</cfif>
		<br>
		Date of Birth: <cfif len(#local.qryGetDOB.DOB#)>#local.qryGetDOB.DOB#<cfelse>(none)</cfif>
		<BR>
		Universal Program Number: <cfif len(arguments.strCredit.courseApproval)>#arguments.strCredit.courseApproval#<cfelse>(Not available)</cfif>
		<br/><br/>
		<table cellpadding="4" cellspacing="0">
		<tr><td class=MsoNormal>Participant started program at</td>
			<td class=MsoNormal align="right">#DateFormat(arguments.enrollmentInfo.dateEnrolled,"m/d/yyyy")# #TimeFormat(arguments.enrollmentInfo.dateEnrolled,"h:mm TT")# CENTRAL</td>
		</tr>
		<tr><td class=MsoNormal>Participant exited program at</td>
			<td class=MsoNormal align="right">#DateFormat(arguments.enrollmentInfo.dateCompleted,"m/d/yyyy")# #TimeFormat(arguments.enrollmentInfo.dateCompleted,"h:mm TT")# CENTRAL</td>
		</tr>
		<tr><td class=MsoNormal>Total time spent in program:</td>
			<td class=MsoNormal align="right">#val(arguments.strCredit.finalTimeSpent)# minutes</td>
		</tr>
		<cfif arguments.qrySeminarSettings.promptInterval gt 0>
			<tr><td class=MsoNormal>Polling/Prompting Requirement Satisfied:</td>
				<td class=MsoNormal align="right">YES</td>
			</tr>
		</cfif>
		<cfif arguments.qrySeminarSettings.evaluationRequired>
			<tr><td class=MsoNormal>Evaluation Requirement Satisfied:</td>
				<td class=MsoNormal align="right">YES</td>
			</tr>
		</cfif>
		<cfif arguments.qrySeminarSettings.examRequired>
			<tr><td class=MsoNormal>Examination Requirement Satisfied:</td>
				<td class=MsoNormal align="right">YES</td>
			</tr>
		</cfif>
		</table>
		<div style="margin-top:0;position:relative;z-index:9">#local.strSWRegistrar.swRegistrarSignature#</div>
		<br/>
		<br/>
		<div style="width:70%; border-top:1px solid ##000;padding-top:4px;margin-top:-25px;position:relative;z-index:10">
		#local.strSWRegistrar.swRegistrarLine#
		</div>
	</td></tr>
<tr><td class=MsoNormal><br/><b>Participant Verification:</b></td>
	<td class=MsoNormal><br/>
		I, #UCASE(arguments.enrollmentInfo.fullname)#, certify that I completed this program in its entirety.<br/>
		Approval Number: <cfif len(arguments.strCredit.courseApproval)>#arguments.strCredit.courseApproval#<cfelse>(Not available)</cfif><cfif len(arguments.strCredit.credits)> - #arguments.strCredit.credits#</cfif><br/>
		<div style="width:70%;text-align:right;margin-top:.2in;z-index:50;">#arguments.strCredit.idNumber#&nbsp;</div>
		<div style="width:70%;border-top:1px solid ##000;padding-top:4px;z-index:51;">
			<div style="display:inline;float:right;text-align:right;">DATE#repeatString("&nbsp; ",15)##arguments.strCredit.creditIDText#</div>
			<div style="display:inline;text-align:left">#UCASE(arguments.enrollmentInfo.fullname)#</div>
		</div>
	</td></tr>
<cfif len(arguments.strCredit.instructions)>
	<tr><td class=MsoNormal><br/><b>Important Instructions:</b></td>
		<td class=MsoNormal><br/>#arguments.strCredit.instructions#</td></tr>
</cfif>
</table>
</div>
</body>
</html>
</cfoutput>