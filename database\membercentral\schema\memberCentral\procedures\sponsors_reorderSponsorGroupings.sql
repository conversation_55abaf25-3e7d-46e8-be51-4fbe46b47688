CREATE PROC dbo.sponsors_reorderSponsorGroupings
@referenceType varchar(50),
@referenceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Determine context and execute appropriate logic
	IF @referenceType = 'Events'
	BEGIN
		DECLARE @tmpEvents TABLE (neworder int NOT NULL, sponsorGrouping<PERSON> int NOT NULL, sponsorGroupingOrder int NOT NULL);

		INSERT INTO @tmpEvents (sponsorGrouping<PERSON>, sponsorGroupingOrder, newOrder)
		SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
		FROM dbo.ev_sponsorGrouping
		WHERE eventID = @referenceID;

		UPDATE sg
		SET sg.sponsorGroupingOrder = t.neworder
		FROM dbo.ev_sponsorGrouping as sg
		INNER JOIN @tmpEvents as t on sg.sponsorGroupingID = t.sponsorGroupingID;
	END
	ELSE IF @referenceType IN ('swlProgram', 'swodProgram', 'swbprogram')
	BEGIN
		DECLARE @tmpSW TABLE (neworder int NOT NULL, sponsorGroupingID int NOT NULL, sponsorGroupingOrder int NOT NULL);

		INSERT INTO @tmpSW (sponsorGroupingID, sponsorGroupingOrder, newOrder)
		SELECT sponsorGroupingID, sponsorGroupingOrder, ROW_NUMBER() OVER(ORDER BY sponsorGroupingOrder) as newOrder
		FROM seminarWeb.dbo.sw_sponsorGrouping
		WHERE seminarID = @referenceID AND participantID = 0;

		UPDATE sg
		SET sg.sponsorGroupingOrder = t.neworder
		FROM seminarWeb.dbo.sw_sponsorGrouping as sg
		INNER JOIN @tmpSW as t on sg.sponsorGroupingID = t.sponsorGroupingID
		WHERE sg.participantID = 0;
	END
	ELSE
	BEGIN
		RAISERROR('Unsupported reference type: %s', 16, 1, @referenceType);
		RETURN -1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
