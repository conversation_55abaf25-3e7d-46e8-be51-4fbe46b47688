CREATE PROC dbo.sponsors_getSponsorGroupingByID
@sponsorGroupingID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- First check if it's an Events sponsor grouping
	IF EXISTS (SELECT sponsorGroupingID FROM dbo.ev_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
	BEGIN
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM dbo.ev_sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;
	END
	-- Then check if it's a SeminarWeb sponsor grouping
	ELSE IF EXISTS (SELECT sponsorGroupingID FROM seminarWeb.dbo.sw_sponsorGrouping WHERE sponsorGroupingID=@sponsorGroupingID)
	BEGIN
		SELECT sponsorGroupingID, sponsorGrouping, sponsorGroupingOrder
		FROM seminarWeb.dbo.sw_sponsorGrouping
		WHERE sponsorGroupingID = @sponsorGroupingID;
	END
	ELSE
	BEGIN
		-- Return empty result set with correct structure
		SELECT TOP 0 
			CAST(0 AS INT) AS sponsorGroupingID, 
			CAST('' AS VARCHAR(200)) AS sponsorGrouping, 
			CAST(0 AS INT) AS sponsorGroupingOrder;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
