<cfsavecontent variable="local.customJS">
	<cfoutput>
	<script type="text/javascript">
		function saveSponsorGroup() {
			mca_hideAlert('err_frmeditsponsorgroup');
			var isEdit = #local.isEdit ? 'true' : 'false'#;
			var saveSponsorGroupResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					if (isEdit) {
						top.onSponsorGroupUpdated_#local.widgetSelectorID#();
					} else {
						top.onSponsorGroupCreated_#local.widgetSelectorID#();
					}
				} else {
					let errMsg = r.errmsg ? r.errmsg : 'We were unable to ' + (isEdit ? 'update' : 'create') + ' this sponsor group. Please try again.';
					mca_showAlert('err_frmeditsponsorgroup', errMsg);
					top.$('##btnMCModalSave').prop('disabled', false).html(isEdit ? 'Save Changes' : 'Create Group');
				}
			};
			if($('##sponsorGroupName').val().trim() != ''){
				top.$('##btnMCModalSave').prop('disabled', true).html(isEdit ? 'Saving...' : 'Creating...');
				var objParams = {
					sponsorGrouping: $('##sponsorGroupName').val().trim()
				};

				objParams.referenceType = '#local.referenceType#';
				objParams.referenceID = #local.referenceID#;
				objParams.participantID = #local.participantID#;

				if (isEdit) {
					objParams.sponsorGroupingID = #local.sponsorGroupingID#;
					TS_AJX('SPONSORS','updateSponsorGrouping',objParams,saveSponsorGroupResult,saveSponsorGroupResult,10000,saveSponsorGroupResult);
				} else {
					TS_AJX('SPONSORS','createSponsorGrouping',objParams,saveSponsorGroupResult,saveSponsorGroupResult,10000,saveSponsorGroupResult);
				}
			} else {
				mca_showAlert('err_frmeditsponsorgroup', 'Please enter a name for the sponsor group.');
				return false;
			}
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.customJS)#">

<cfoutput>
<form name="frmEditSponsorGroup" id="frmEditSponsorGroup" onsubmit="saveSponsorGroup(); return false;">

	<div id="err_frmeditsponsorgroup" class="alert alert-danger mb-3 d-none"></div>

	<div class="form-label-group mb-3">
		<input type="text" name="sponsorGroupName" id="sponsorGroupName" class="form-control"
			   value="#encodeForHTMLAttribute(local.currentGroupName)#" maxlength="100" required>
		<label for="sponsorGroupName">Sponsor Group Name</label>
	</div>

	<!--- Hidden submit button triggered from parent modal --->
	<button type="submit" class="d-none"></button>
</form>
</cfoutput>
