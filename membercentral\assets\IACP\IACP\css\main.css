/* tsApps styles and overrides */
@import url("/assets/common/css/tsApps.css");

#container{position:relative;width:787px;margin:auto auto;background:url(../images/mainBG.jpg) repeat-y;text-align:left;}
#mainContent{position:relative;width:750px;margin-left:100px;margin-top:10px;padding-bottom:30px;}
#links{text-align:right;margin-right:15px;font-family:arial;font-size:13px;}
#links a{text-decoration:none;}
#links a:hover{text-decoration:underline;}
#footer{position:relative;width:787px;margin:auto auto;}
#systemToolbar{position:relative;margin:auto auto;}
#systemToolbar a{color:#fff;}