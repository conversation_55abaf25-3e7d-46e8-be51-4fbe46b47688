<cfscript>
	variables.applicationReservedURLParams 	= "issubmitted";
	local.customPage.baseURL								= "/?#getBaseQueryString(false)#";
	// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
	local.arrCustomFields = [];
	local.tmpField = { name="PayProfileCodeCC", type="STRING", desc="Profile CodeCC", value="AuthorizeCIM" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="PayProfileCodeBD", type="STRING", desc="Profile CodeBD", value="MATABD" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="FormNameDispaly", type="STRING", desc="Display Form Name", value="Membership Application Form" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="StaffEmail", type="STRING", desc="Email to send confirmations", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="memberEmailFrom", type="STRING", desc="Member Email From", value="<EMAIL>"};  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AccountLookUpTitle", type="STRING", desc="Account look up title", value="Account Lookup / Create New Account" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AccountLookUpButton", type="STRING", desc="Account look up Button Name", value="Account Lookup" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="AccountLookUpContent", type="CONTENTOBJ", desc="Account look up Content", value='
		<div style="padding-bottom:5px;">Click the <span ><b>Account Lookup</b></span> button to the left.</div>
		<div style="padding-bottom:5px;">Enter the search criteria and click <span><b>Continue</b></span>.</div>
		<div style="padding-bottom:5px;">If you see your name, please press the <span ><b>Choose</b></span> button next to your name.</div>
		<div style="padding-bottom:5px;">If you do not see your name, click the <span ><b>Create an Account</b></span> link.</div>
	' };  
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Payment Confirmation Message", value='
		<div class="HeaderText">Thank you for submitting your application!</div>
			<br/>
			<div>This page has been emailed to the email address on file. If you would like, you could also print the page out as a receipt.</div>
	' }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="lessThan2YrsText", type="STRING", desc="Less Than Two Year Text", value="Licensed less than two (2) years" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="lessThan2YrsAmt", type="STRING", desc="Less Than Two Year Amount", value="175" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="lessThan5YrsTxt", type="STRING", desc="Less Than Five Year Text", value="Licensed less than five (5) years" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="lessThan5YrsAmt", type="STRING", desc="Less Than Five Year Amount", value="225" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="moreThan5YrsTxt", type="STRING", desc="More Than Five Year Text", value="Licensed five (5) years or more" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="moreThan5YrsAmt", type="STRING", desc="More Than Five Year Amount", value="425" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="sustainingTxt", type="STRING", desc="Sustaining Text", value="Sustaining" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="sustainingAmt", type="STRING", desc="Sustaining Amount", value="650" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="govtRateTxt", type="STRING", desc="Government Rate Text", value="Government Rate" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="govtRateAmt", type="STRING", desc="Government Rate Amount", value="150" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="outOfStateTxt", type="STRING", desc="Out of State Text", value="Out of State" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="outOfStateAmt", type="STRING", desc="Out of State Amount", value="425" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="legAssessmentTxt", type="STRING", desc="Leg Assessment Text", value="MATA's Legislative Program -- Optional Legislative Assessment" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="legAssessmentAmt", type="STRING", desc="Leg Assessment Amount", value="200" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="ProfessionalLicenseInstructionsText", type="CONTENTOBJ", desc="Professional License Instruction Text at the top", value="Please select and update all State Bar licenses that apply with your license number and active Bar date." };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.tmpField = { name="MembershipLevelInstructionText", type="CONTENTOBJ", desc="Membership Level Instruction Text at the top", value="<div class=""c""><strong>REQUIRED NOTICE</strong></div><br />In compliance with IRS code Section 6113, the following statement is provided:<br />""Contributions to the Missouri Association of Trial Attorneys are not deductible as charitable contributions for federal income tax purposes.""<br /><br />Membership dues payments to the Missouri Association of Trial Attorneys (MATA) are not tax-deductible as charitable contributions for income tax purposes. However, they may be tax-deductible as ordinary and necessary business expenses subject to restrictions imposed as a result of the Association's lobbying activities. MATA estimates that the non-deductible portion of your current annual dues -- the portion which is allocable for lobbying -- is 50%." };  	
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MataMembershipWaiver", type="CONTENTOBJ", desc="MATA Membership Waiver", value='
		I do a variety of work or have long-standing clients that result in occasional defense work. I am requesting a waiver to allow my membership in MATA.
	' }; 
	arrayAppend(local.arrCustomFields, local.tmpField)
	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);	

	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
		formName='frmJoin',
		formNameDisplay='#local.strPageFields.FormNameDispaly#',
		orgEmailTo='#local.strPageFields.StaffEmail#',
		memberEmailFrom='#local.strPageFields.memberEmailFrom#'
	));
	
	// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
	local.profile_1._profileCode 	= local.strPageFields.PayProfileCodeCC;
	local.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=local.siteID,profileCode=local.profile_1._profileCode);

	local.profile_2._profileCode 	= local.strPageFields.PayProfileCodeBD;
	local.profile_2._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=local.siteID,profileCode=local.profile_2._profileCode);

	//----------------------------------------------------------------
	if( application.objUser.isLoggedIn(cfcuser=session.cfcuser) ){
		local.customFields = arrayNew(1);
		arrayAppend(local.customFields,'Members Listserve');
		arrayAppend(local.customFields,'Plaintiff Listserve');
		arrayAppend(local.customFields,'Eclips Service');
		local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=local.orgID, memberNumber=session.cfcUser.memberData.memberNumber, customFields=local.customFields);
	} else{
		local.memberData['Members Listserve'] = '';
		local.memberData['Plaintiff Listserve'] = '';
		local.memberData['Eclips Service'] = '';
	}

	local.data.fax	= application.objMember.getMemberPhones(orgID=local.orgID, memberID=local.memberID, addressTypeID=109);
</cfscript>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Tahoma, Verdana, Arial, Helvetica, sans-serif;}
			.frmContent{ padding:10px; background:##dddddd; }
			.frmRow1{ background:##ffffff; }
			.frmRow2{ background:##dedede; }
			.frmRow3{ background:##aeaeae; }
			.frmTotals{ background:##666666; color:##ffffff; font-weight:bold; }
			.frmText{ font-size:9pt; color:##505050; }
			.frmButtons{ padding:5px 0; border-top:1px solid ##666666; border-bottom:1px solid ##666666; }
			.PageTitleText { font-family:Tahoma; font-size:16pt; color:##03608b; font-weight:bold; }
			.CPSection{ border:1px solid ##666666; margin-bottom:15px; }
			.CPSectionTitle { font-size:14pt; height:35px; font-weight:bold; color:##ffffff; padding:10px; background:##336699; }
			.CPSectionContent{ padding:0 10px; }
			.subCPSectionArea1 { padding:10px 15px 10px 15px; background-color:##cde4f3; }
			.subCPSectionArea2 { padding:10px 15px 10px 15px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:10px 15px 10px 15px; background-color:##aaaaaa;}
			.subCPSectionTitle { font-size:10pt; font-weight:bold; }
			.subCPSectionText { font-size:9pt; color:##36617d; }
			.info{ font-style:italic; font-size:7pt; color:##777777; }
			.small{ font-size:7pt;}
			.r { text-align:right; }
			.l { text-align:left; }
			.c { text-align:center; }
			.i { font-style:italic; }
			.b{ font-weight:bold; }
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			.BB { border-bottom:1px solid ##666666; }
			.BL { border-left:1px solid ##666666; }
			.BT { border-top:1px solid ##666666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{ background:##224563; color:##ffffff; font-weight:bold; text-transform:uppercase; padding:5px; }
			.msgSubHeader{background:##dddddd;}
			.tsAppBodyText { color:##03608b;}
			select.tsAppBodyText{color:##666666;}
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			input[type="radio"],input[type="checkbox"] {
				margin:0 0 2px; 
				line-height: normal
			}
		</style>
	</cfsavecontent>
	
	<cfsavecontent variable="local.pageJS">
		<script type="text/javascript">
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function _FB_withinResponseLimit(obj, obj_type, optRespLimit) {
				var cnt = 0;
				for (var i=0; i < obj.length; i++) {
					if (obj_type == 'SELECT' && obj.options[i].selected) cnt++;
					else if (obj_type == 'CHECKBOX' && obj[i].checked) cnt++;
				}					
				if (cnt <= optRespLimit) return true;
				else return false;
			}
			function getSelectedRadio(buttonGroup) {
				if (buttonGroup[0]) {
					for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
				} else { if (buttonGroup.checked) return 0; }
				return -1;
			}
			function selectMember() {
				$.colorbox( {innerWidth:550, innerHeight:330, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember', iframe:true, overlayClose:false} );
			}
			function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
			function addMember(memObj) {
				$.colorbox.close();
				assignMemberData(memObj);
			}
		</script>
	</cfsavecontent>
	
	#local.pageJS#
	#local.pageCSS#
	
	<div id="customPage">
		<div class="PageTitleText" style="padding-bottom:15px;text-align:center;">#local.organization# <br />#local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: ========================================================================================================================================= --->
			<cfcase value="0">
				<cfset local.profLicenseIDList = "">

				<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=local.orgID)>
				<cfset local.licenseStatus = {}>
				<cfset local.index = 1>
				<cfloop query="local.qryOrgProLicenseStatuses">
					<cfset local.licenseStatus[local.index] = local.qryOrgProLicenseStatuses.statusName>	
					<cfset local.index = local.index + 1>
				</cfloop> 
				<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.orgID)>
				
				<script type="text/javascript">
					function _FB_validateForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						var isProfLicenseRequired = true;
						if (!_FB_hasValue(thisForm['firstName'], 'TEXT')) arrReq[arrReq.length] 				= 'First Name';
						if (!_FB_hasValue(thisForm['lastName'], 'TEXT')) arrReq[arrReq.length] 					= 'Last Name';
						if (!_FB_hasValue(thisForm['firmName'], 'TEXT')) arrReq[arrReq.length] 					= 'Firm Name';
						if (!_FB_hasValue(thisForm['address'], 'TEXT')) arrReq[arrReq.length] 					= 'Office Address';
						if (!_FB_hasValue(thisForm['city'], 'TEXT')) arrReq[arrReq.length] 							= 'City';
						if (!_FB_hasValue(thisForm['state'], 'TEXT')) arrReq[arrReq.length] 						= 'State';
						if (!_FB_hasValue(thisForm['zip'], 'TEXT')) arrReq[arrReq.length] 							= 'Zip';
						if (!_FB_hasValue(thisForm['county'], 'TEXT')) arrReq[arrReq.length] 						= 'County';
						if (!_FB_hasValue(thisForm['phone'], 'TEXT')) arrReq[arrReq.length] 						= 'Phone';
						if (!_FB_hasValue(thisForm['email'], 'TEXT')) arrReq[arrReq.length] 						= 'Email Address';
						
						if(isProfLicenseRequired){
							var prof_license = $('.mpl_pltypeid').val();
							var isProfLicenseSelected = false;
							if(prof_license != "" && prof_license != null){
								isProfLicenseSelected = true;
								$.each(prof_license,function(i,val){
									var text = $("##mpl_"+val+"_licenseNumber").parent().prev().text();                                    
									if($("##mpl_"+val+"_activeDate").val().length == 0){ isProfLicenseSelected = false; arrReq[arrReq.length] = 'Enter your  '+text +' License Date.'; }
								});
							}
							if (!isProfLicenseSelected)	arrReq[arrReq.length] = "Professional License is required";
						}
						if (!_FB_hasValue(thisForm['applicantsPlaintiffPercentage'], 'TEXT')) arrReq[arrReq.length] 			= 'Please enter a percentage of Plaintiffs';
						if (!_FB_hasValue(thisForm['firmsPlaintiffPercentage'], 'TEXT')) arrReq[arrReq.length] 			= 'Please enter a percentage of Plaintiffs';
						
							if (!_FB_hasValue(thisForm['Membership'], 'RADIO')) arrReq[arrReq.length] 			= 'Membership level';
						
							if (!_FB_hasValue(thisForm['consent'], 'SINGLE_VALUE_CHECKBOX')) arrReq[arrReq.length] 	= 'Please agree to the Membership Statement';
							if (!_FB_hasValue(thisForm['changeFirmsAcceptance'], 'SINGLE_VALUE_CHECKBOX')) arrReq[arrReq.length] 	= 'Please accept the change of firms statement';
						
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						
						$("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
						return true;
					}
					function assignMemberData(memObj){
						
						$('##isNewRecord').val(memObj.isNewRecord);
						var thisForm = document.forms["#local.formName#"];
						var er_change = function(r) {
							var results = r;
							if( results.success ){
								thisForm['memberNumber'].value 	= results.membernumber;
								thisForm['memberID'].value 			= results.memberid;

								thisForm['firstName'].value 		= results.firstname;
								thisForm['middleName'].value 		= results.middlename;
								thisForm['lastName'].value 			= results.lastname;
								thisForm['suffix'].value 			= results.suffix;
								thisForm['firmName'].value			= results.company;
								thisForm['address'].value 			= results.address1;
								thisForm['address2'].value 				= results.address2;
								thisForm['city'].value 					= results.city;
								thisForm['state'].value 				= results.statecode;
								thisForm['zip'].value 					= results.postalcode;
								thisForm['county'].value 				= results.county;
								thisForm['phone'].value 				= results.phone;
								thisForm['email'].value 				= results.email;
								thisForm['isNewRecord'].value = results.isnewmember;

								if (typeof results.licensenumber != "undefined" && results.licensenumber != '') {
									if($('.mpl_pltypeid').length > 0){
										$.each( results.licenses, function( index, value ){
											$('.mpl_pltypeid option[value=' + value.pltypeid + ']').attr('selected', true);
											$('.mpl_pltypeid').multiselect("refresh");
											var pltypeid = value.pltypeid;
											var license_name  = value.plname;
											var license_no  = value.licensenumber;
											var license_date  = value.activedate;
											var license_status  = value.statusname;
											licenseChange(true,pltypeid,license_name,license_no,license_date,license_status);
										});
									}
                                }
								//Custom Fields--------------------------------------------------------------------------
								if ( results["members listserve"] == 1 ){
									thisForm['membersListserve'].checked		= true;
								}
								if ( results["plaintiff listserve"] == 1 ){
									thisForm['plaintiffListserve'].checked	= true;
								}
								if ( results["eclips service"] == 1 ){
									thisForm['eclipsService'].checked				= true;
								}
								if ( results["motlc"] == 1 ){
									thisForm['moTLC'].checked				= true;
								}
								if ( results["emergency response volunteer"] == 1 ){
									thisForm['emergencyResponse'].checked				= true;
								}
								//---------------------------------------------------------------------------------------
								checkBarDate();
								// un hide form   
								document.getElementById('formToFill').style.display 			= '';
							}
							else{ /*alert('not success');*/ }
						};
						/************************************************************************************************/
						var arrKeys = ["Members Listserve", "Plaintiff Listserve", "Eclips Service", "Emergency Response Volunteer", "MoTLC"] ;
						var objParams = { memberNumber:memObj.memberNumber, customfields:arrKeys };
						TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,er_change,er_change,1000000,er_change);
					}
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}
					function checkBarDate() {
						var activeDate = oldestActiveDate();
						var barDate = new Date(activeDate);
						var barYear = barDate.getFullYear();
						var barMonth = barDate.getMonth() + 1;
						var barDay = barDate.getDate();
						var currDate = new Date();
						var currYear = currDate.getFullYear();
						var currMonth = currDate.getMonth() + 1;
						var currDay = currDate.getDate();
						var yearDiff = currYear - barYear;

						if (barYear && $.trim(activeDate).length) {
							if (yearDiff < 2) {
								//<2 years	
								selectMemberType(1);
							}

							if (yearDiff == 2) {
								if (barMonth > currMonth) {
									//<2 years
									selectMemberType(1);
								} else if (barMonth == currMonth) {
									if (barDay > currDay) {
										//2 - 5 years
										selectMemberType(2);
									} else {
										//<2 years
										selectMemberType(1);
									}
								} else {
									//2 - 5 years	
									selectMemberType(2);
								}
							}

							if (yearDiff > 2 && yearDiff <= 4) {
								//2 - 5 years	
								selectMemberType(2);
							}

							if (yearDiff == 5) {
								if (barMonth > currMonth) {
									//2 - 5 years
									selectMemberType(2);
								} else if (barMonth == currMonth) {
									if (barDay > currDay) {
										//2 - 5 years
										selectMemberType(2);
									} else {
										//5+ years
										selectMemberType(3);
									}
								} else {
									//5+ years	
									selectMemberType(3);
								}
							}

							if (yearDiff > 5) {
								//5+ years
								selectMemberType(3);
							}
						} else {
							selectMemberType(0);
						}
					}
					function selectMemberType(toSelect){
						var memberRadios = $('[name="Membership"]');
						//disable all radios
						memberRadios[0].disabled 	= true;
						memberRadios[0].checked 	= false;
						memberRadios[1].disabled 	= true;
						memberRadios[1].checked 	= false;
						memberRadios[2].disabled 	= true;
						memberRadios[2].checked 	= false;
						
						//enable and select appropriate one
						switch(toSelect){
							case 1:
								memberRadios[0].disabled 	= false;
								memberRadios[0].checked 	= true;
							break;
							case 2:
								memberRadios[1].disabled 	= false;
								memberRadios[1].checked 	= true;
							break;
							case 3:
								memberRadios[2].disabled 	= false;
								memberRadios[2].checked 	= true;
							break;
						}					
					}
					function checkPhFormat(x){
						var numberEntered = x.value.toString();
						var fieldEntered = x;
						var messageField = document.getElementById(x.id + '_message');
						var pattern = /\d{3}-\d{3}-\d{4}/;
						var match = pattern.test(numberEntered);
						
						if ( match == false ){
							fieldEntered.value = '';
							messageField.style.display = '';
						} else {
							messageField.style.display = 'none';	
						}
					}
					function licenseChange(isChecked,val,licenseName,licenseNumber,activeDate,status){
						$("##state_table").show();
						if(status == ''){
							status = 'Active';
						}
						strOption = '';
						<cfloop collection="#local.licenseStatus#" item="local.i" >
							strOption += '<option value="#local.licenseStatus[local.i]#">#local.licenseStatus[local.i]#</option>';
						</cfloop>
						if(isChecked){
							$('##selectedLicense').append('<tr id="tr_state_'+val+'">'+
									'<td class="frmText">'+licenseName+'</td>'+
									'<td><input name="mpl_'+val+'_licenseNumber" id="mpl_'+val+'_licenseNumber" class="tsAppBodyText" type="text" value="'+licenseNumber+'" size="13" maxlength="13"></td>'+ 
									'<input name="mpl_'+val+'_licenseName" id="mpl_'+val+'_licenseName" type="hidden" value="'+licenseName+'" />'+
									'<td><input name="mpl_'+val+'_activeDate" id="mpl_'+val+'_activeDate" type="text" value="'+activeDate+'" class="tsAppBodyText" size="13" maxlength="10" onChange="checkBarDate();"></td>'+
									'<td style="visibility:hidden"><select name="mpl_'+val+'_status" id="mpl_'+val+'_status" class="tsAppBodyText">'+strOption+'</select></td>'+
									'</tr>');
							$('##mpl_'+val+'_status').val(status);
							mca_setupDatePickerField('mpl_'+val+'_activeDate');
							$('##mpl_'+val+'_activeDate').attr('style', 'background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; cursor:pointer !important');							
						}									
						else{
							$("##tr_state_"+val).remove();								
						}
						if($('##selectedLicense tr').length == 0){
							$("##state_table").hide();
						}	
					}
					function oldestActiveDate(){
						var oldestDate = "";
						var missFlag = false;
						if($('##selectedLicense input[type="hidden"]').length){
							$('##selectedLicense input[type="hidden"]').each(function(){
								var activeDate = $('##'+$(this).attr('id').split('_')[0]+'_'+$(this).attr('id').split('_')[1]+'_activeDate').val();
								if(missFlag == false && activeDate.length){						
									if($.trim(oldestDate).length == 0){
										oldestDate = activeDate;
									}else if(activeDate < oldestDate){
										oldestDate = activeDate;
									}
									if($(this).val() == 'Missouri'){
										oldestDate = activeDate;
										missFlag = true;
									}
								}
							});
						}else{
							oldestDate = "";
						}
						return oldestDate;
					}
					$(document).ready(function() {	
						$(".mpl_pltypeid").multiselect({
							header: true,
							noneSelectedText: ' - Please Select - ',
							selectedList: 1,
							minWidth: 400,
							click: function(event, ui){
								licenseChange(ui.checked,ui.value,ui.text,'','','');      
								checkBarDate();                      										
							},
							uncheckAll: function(){
								$(".mpl_pltypeid option").each(function(){
									$('##tr_state_'+$(this).attr("value")).remove();
								});
								if($('##selectedLicense tr').length == 0){
									$("##state_table").hide();
								}	
								checkBarDate();
							},
							checkAll: function( e ){
								$(".mpl_pltypeid option").each(function(){
									licenseChange(true,$(this).attr("value"),$(this).text(),'','','');	
								});
								checkBarDate();
							}
						});
					});
				</script>
				<div class="r i frmText">*Denotes required field</div>
				<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="">
					<!--- ACCOUNT LOCATOR: ============================================================================================================================== --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">#local.strPageFields.AccountLookUpTitle#</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%">
									<tr>
										<td width="175" class="c">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">#local.strPageFields.AccountLookUpButton#</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												#local.strPageFields.AccountLookUpContent#
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</cfif>
					
					<div id="formToFill" style="display:none;">
						<!--- PERSONAL INFORMATION: ========================================================================================================================= --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Contact Information</div>
							<div class="frmRow1 frmText" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="r" width="200">Prefix:</td>
										<td>
											<input name="prefix" type="radio" value="Mr." class="tsAppBodyText" />Mr.&nbsp;&nbsp;
											<input name="prefix" type="radio" value="Ms." class="tsAppBodyText" />Ms.&nbsp;&nbsp;
											<input name="prefix" type="radio" value="Mrs." class="tsAppBodyText" />Mrs.&nbsp;&nbsp;
										</td>
									</tr>
									<tr>
										<td class="r" width="200">*First Name:</td>
										<td><input size="40" name="firstName" type="text" value="#session.cfcUser.memberData.firstname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r" width="200">Middle Name:</td>
										<td><input size="40" name="middleName" type="text" value="#session.cfcUser.memberData.middlename#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Last Name:</td>
										<td><input size="40" name="lastName" type="text" value="#session.cfcUser.memberData.lastname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r" width="200">Suffix:</td>
										<td><input size="40" name="suffix" type="text" value="#session.cfcUser.memberData.suffix#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Firm Name:</td>
										<td><input size="60" name="firmName" type="text" value="#session.cfcUser.memberData.Company#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Office Address:</td>
										<td><input size="60" name="address" type="text" value="#local.data.address.address1#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">&nbsp;</td>
										<td><input size="60" name="address2" type="text" value="#local.data.address.address2#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*City:</td>
										<td class=" frmText">
											<input size="25" name="city" type="text" value="#local.data.address.city#" class="tsAppBodyText" />
										</td>
									</tr>
									<tr>
										<td class="r">*State:</td>
										<td class=" frmText">
											<input size="2" maxlength="2" name="state" type="text" value="#local.data.address.stateCode#" class="tsAppBodyText" />
										</td>
									</tr>	
									<tr>
										<td class="r">*Zip:</td>
										<td class=" frmText">
											<input size="10" maxlength="15" name="zip" type="text" value="#local.data.address.postalCode#" class="tsAppBodyText" />
										</td>
									</tr>																										
									<tr>
										<td class="r">*County:</td>
										<td><input size="60" name="county" type="text" value="#local.data.address.county#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">*Phone:</td>
										<td class=" frmText">
											<input size="13" maxlength="13" name="phone" id="phone" type="text" value="#local.data.phone.phone#" class="tsAppBodyText" onchange="checkPhFormat(this);" />
										</td>
									</tr>
									<tr>
										<td class="r">Fax:</td>
										<td class=" frmText">
											<input size="13" maxlength="13" name="fax" id="fax" type="text" value="#local.data.fax.phone#" class="tsAppBodyText" onchange="checkPhFormat(this);" />
										</td>
									</tr>																		
									<tr id="phone_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter phone number in the format: ************</div></td></tr>
									<tr id="fax_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter fax number in the format: ************</div></td></tr>
									<tr>
										<td class="r">*Email Address:</td>
										<td><input size="60" name="email" type="text" value="#session.cfcUser.memberData.email#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">Recruited By:</td>
										<td>
										<input size="60" name="recruitedBy" type="text" value="" class="tsAppBodyText" />
										</td>
									</tr>
									<tr>
										<td colspan="2">
										Please supply your home address so MATA can connect you with your legislators. We do not provide your home address to any other entity for any reason.
										</td>
									</tr>
									<tr>
										<td class="r">Home Address:</td>
										<td><input size="60" name="home_address" id="home_address" type="text" value="#local.data.address.address1#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">&nbsp;</td>
										<td><input size="60" name="home_address2" id="home_address2" type="text" value="#local.data.address.address2#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="r">City:</td>
										<td class=" frmText">
											<input size="25" name="home_city" id="home_city" type="text" value="#local.data.address.city#" class="tsAppBodyText" />
										</td>
									</tr>
									<tr>
										<td class="r">State:</td>
										<td class=" frmText">
											<input size="2" maxlength="2" name="home_state" id="home_state" type="text" value="#local.data.address.stateCode#" class="tsAppBodyText" />
										</td>
									</tr>	
									<tr>
										<td class="r">Zip:</td>
										<td class=" frmText">
											<input size="10" maxlength="15" name="home_zip" id="home_zip" type="text" value="#local.data.address.postalCode#" class="tsAppBodyText" />
										</td>
									</tr>																										
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB">*Professional License Information</div>
							<div class="subCPSectionArea1 BB">
								<div class="subCPSectionText">
									#local.strPageFields.ProfessionalLicenseInstructionsText#
								</div>
							</div>
							<div class="frmRow1 frmText" style="padding:10px;">									
								<table cellpadding="3" border="0" cellspacing="0" >									
									<tr align="top">
										<td class="frmText" width="10">&nbsp;</td>
										<td class="frmText" nowrap>Professional License</td>
										<td class="frmText">
											<select name="mpl_pltypeid" class="mpl_pltypeid tsAppBodyText" multiple="multiple">
												<cfloop query="local.qryOrgPlTypes">	
													<option value="#local.qryOrgPlTypes.pltypeid#" <cfif ListFindNoCase(local.profLicenseIDList, local.qryOrgPlTypes.pltypeid)>selected="selected"</cfif>>#local.qryOrgPlTypes.PLName#</option>																						
												</cfloop>
											</select>
										</td>
									</tr>
									<tr class="top">
										<td class="frmText" width="10"></td>
										<td class="frmText"></td>
										<td class="frmText"></td>
									</tr>
								</table>
								<table style="display:none;" id="state_table" cellpadding="3" border="0" cellspacing="0">
									<thead>
										<tr valign="top">
											<th align="left" class="frmText proLicenseLabel" width="150px">State Name</th>
											<th align="center" class="frmText proLicenseLabel">#local.strProfLicenseLabels.profLicenseNumberLabel#</th>
											<th align="center" class="frmText proLicenseLabel">#local.strProfLicenseLabels.profLicenseDateLabel# (mm/dd/yyyy)</th>
											<th align="center" class="frmText proLicenseLabel"  style="visibility:hidden"><b>#local.strProfLicenseLabels.profLicenseStatusLabel#</b></th>
										</tr>
									</thead>
									<tbody id="selectedLicense">              
									</tbody>
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB">*Membership Level</div>
							<div class=" subCPSectionArea1 BB">
								<div class="subCPSectionText">
									#local.strPageFields.MembershipLevelInstructionText#
								</div>
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P c" width="25"><input type="radio" value="lessThan2Yrs" name="Membership" /></td>
										<td>#local.strPageFields.lessThan2YrsText#</td>
										<td width="150" class="P r">#dollarFormat(local.strPageFields.lessThan2YrsAmt)#</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P c"><input type="radio" value="lessThan5Yrs" name="Membership" /></td>
										<td>#local.strPageFields.lessThan5YrsTxt#</td>
										<td class="P r">#dollarFormat(local.strPageFields.lessThan5YrsAmt)#</td>
									</tr>
									<tr class="frmRow2"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P c"><input type="radio" value="moreThan5Yrs" name="Membership" /></td>
										<td>#local.strPageFields.moreThan5YrsTxt#</td>
										<td class="P r">#dollarFormat(local.strPageFields.moreThan5YrsAmt)#</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P c"><input type="radio" value="sustaining" name="Membership" /></td>
										<td>#local.strPageFields.sustainingTxt#</td>
										<td class="P r">#dollarFormat(local.strPageFields.sustainingAmt)#</td>
									</tr>
									<tr class="frmRow2"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P c"><input type="radio" value="govt" name="Membership" /></td>
										<td>#local.strPageFields.govtRateTxt#</td>
										<td class="P r">#dollarFormat(local.strPageFields.govtRateAmt)#</td>
									</tr>
									<tr class="frmRow1"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P c"><input type="radio" value="outOfState" name="Membership" /></td>
										<td>#local.strPageFields.outOfStateTxt#</td>
										<td class="P r">#dollarFormat(local.strPageFields.outOfStateAmt)#</td>
									</tr>
									<tr class="frmRow2"><td colspan="3" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P c"><input type="checkbox" value="legAssessment" name="legislativeAssessment" id="legislativeAssessment" /></td>
										<td>#local.strPageFields.legAssessmentTxt#</td>
										<td class="P r">#dollarFormat(local.strPageFields.legAssessmentAmt)#</td>
									</tr>
									<tr class="frmRow2"><td colspan="3"><img src="/assets/common/images/spacer.gif"></td></tr>
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB">Membership Benefits</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P r" width="5px"><input type="checkbox" value="Yes" name="membersListserve" /></td>
										<td class="P">
											<strong>Members Listserv</strong> - By choosing to join the Members Listserver, I am confirming that I have downloaded the <a href="/docDownload/717071" target="_blank">MATA Members List Agreement</a> and agree to abide by all terms and conditions within the agreement.
										</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P r" width="5px"><input type="checkbox" value="Yes" name="plaintiffListserve" /></td>
										<td class="P"><strong>MATA Plaintiffs Listserv</strong> (Inclusion on this list requires completion and return of the "<a href="/docDownload/44254" target="_blank">Plaintiff's Bar Online Information Exchange Rules, Regulations, and Agreement</a>")</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P r" width="5px"><input type="checkbox" value="Yes" name="eclipsService" /></td>
										<td class="P">Yes, I want to receive <strong>E-Clips</strong>, MATA's daily e-mail news service.</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P r" width="5px"><input type="checkbox" value="Yes" name="trialsmith" /></td>
										<td class="P">Yes, I want information about <strong>TrialSmith</strong>, online litigation tools for plaintiff lawyers.</td>
									</tr>
									<tr class="frmRow2"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P r" width="5px"><input type="checkbox" value="Yes" name="emergencyResponse" /></td>
										<td class="P">Yes, I want to be contacted about joining MATA's <strong>Emergency Response Team</strong>.</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2">
										<td class="P r" width="5px"><input type="checkbox" value="Yes" name="moTLC" /></td>
										<td class="P">Yes, I want information about working with <strong>Mo-TLC</strong>, MATA's charitable arm.</td>
									</tr>
									<tr class="frmRow2"><td colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow1">
										<td class="P r" width="5px"><input type="checkbox" value="Yes" name="moLobbyistForADay" id="moLobbyistForADay" /></td>
										<td class="P">Yes, I want to be a Lobbyist for a Day. Please contact me with more information to schedule a date.</td>
									</tr>
									<tr class="frmRow1"><td class="BB" colspan="2"><img src="/assets/common/images/spacer.gif"></td></tr>
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB">*Membership Statements</div>
							<div class=" subCPSectionArea1 BB">
								<div class=" subCPSectionText">
									I certify that I am a duly licensed attorney in good standing and spend a substantial portion of my time representing the 
									interests and defending the rights of plaintiffs in the civil justice system and that I do not spend a majority of my time 
									in the defense of personal injury litigation.
								</div>
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tbody>
										<tr class="frmRow1">
											<td class="P" colspan="2">
												<label for="consent">
													<input type="checkbox" name="consent" id="consent" value=""> &nbsp; I Agree
												</label>
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="BB" colspan="2">
												<img src="/assets/common/images/spacer.gif">
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="P">
												What percentage of YOUR practice is spent representing plaintiffs?
											</td>
											<td class="P" nowrap>
												<input size="5" name="applicantsPlaintiffPercentage" id="applicantsPlaintiffPercentage" type="number" min="0" max="100" value="" class="tsAppBodyText" style="display: inline; width: 70px; float: none;" />%
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="BB" colspan="2">
												<img src="/assets/common/images/spacer.gif">
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="P">
												What percentage of YOUR FIRM's practice is spent representing plaintiffs?
											</td>
											<td class="P" nowrap>
												<input size="5" name="firmsPlaintiffPercentage" id="firmsPlaintiffPercentage" type="number" min="0" max="100" value="" class="tsAppBodyText" style="display: inline; width: 70px; float: none;" />%
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="BB" colspan="2">
												<img src="/assets/common/images/spacer.gif">
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="P" colspan="2">
												<label for="changeFirmsAcceptance">
													<input type="checkbox" name="changeFirmsAcceptance" id="changeFirmsAcceptance" value=""> &nbsp; If I change firms, I understand I will be required to resubmit a membership application.
												</label>
											</td>
										</tr>
										<tr class="frmRow2">
											<td class="BB" colspan="2">
												<img src="/assets/common/images/spacer.gif">
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="P" colspan="2">
												<label for="mataMembershipWaiver">
													<input type="checkbox" name="mataMembershipWaiver" id="mataMembershipWaiver" value="1">  &nbsp; #replace(local.strPageFields.MataMembershipWaiver,'<p>','')#
												</label>
											</td>
										</tr>
										<tr class="frmRow1">
											<td class="BB" colspan="2">
												<img src="/assets/common/images/spacer.gif">
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
									
						<div id="formButtons">
							<div style="padding:10px;">
								<div align="center" class="frmButtons">
									<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
								</div>
							</div>
						</div>
					</div>
				</cfform>

				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>
						$(document).ready(function() {
							loadMember('#session.cfcUser.memberData.memberNumber#');
						});
					</script>
				</cfif>
				
			</cfcase>
			
			<!--- PAYMENT INFO: ================================================================================================================================= --->
			<cfcase value="1">
				<cfscript>
					// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
					local.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
																			siteid=local.siteID,
																			profilecode=local.profile_1._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopup=false,
																			usePopupDIVName='ccForm',
																			autoShowForm=1
																		);
					local.profile_2.strPaymentForm = application.objPayments.showGatewayInputForm(
																			siteid=local.siteID,
																			profilecode=local.profile_2._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopup=false,
																			usePopupDIVName='bdForm',
																			autoShowForm=1
																		);
				</cfscript>
				<script type="text/javascript">
					function checkPaymentMethod() {
						var rdo = document.forms["#local.formName#"].payMeth;
						if (rdo[0].checked) {
							$('##CCInfo').show();
							$('##BDInfo').hide();
							$('##CheckInfo').hide();
						} else if (rdo[1].checked) {
							$('##CCInfo').hide();
							$('##BDInfo').show();
							$('##CheckInfo').hide();
						}  else if (rdo[2].checked) {
							$('##CCInfo').hide();
							$('##BDInfo').hide();
							$('##CheckInfo').show();
						}  
					}
					function getMethodOfPayment() {
						var btnGrp = document.forms['#local.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					function _validate() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#local.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatementCC'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						} else if ( MethodOfPaymentValue == 'BD' )	{
							#local.profile_2.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatementBD'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}
						
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						$("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
						return true;
					}
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
				</script>
				<cfif len(local.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
				</cfif>
				<cfif len(local.profile_2.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_2.strPaymentForm.headCode)#">
				</cfif>
				
				<cfscript>
					switch(event.getValue('Membership')){
						case 'lessThan2Yrs': local.memberShipDues = #local.strPageFields.lessThan2YrsAmt#; local.memberShipName = #local.strPageFields.lessThan2YrsText#; break;
						case 'lessThan5Yrs': local.memberShipDues = #local.strPageFields.lessThan5YrsAmt#; local.memberShipName = #local.strPageFields.lessThan5YrsTxt#; break;
						case 'moreThan5Yrs': local.memberShipDues = #local.strPageFields.moreThan5YrsAmt#; local.memberShipName = #local.strPageFields.moreThan5YrsTxt#; break;
						case 'sustaining': 	 local.memberShipDues = #local.strPageFields.sustainingAmt#; 	local.memberShipName = #local.strPageFields.sustainingTxt#; 	 break;
						case 'govt': 				 local.memberShipDues = #local.strPageFields.govtRateAmt#; 				local.memberShipName = #local.strPageFields.govtRateTxt#; break;
						case 'outOfState': 	 local.memberShipDues = #local.strPageFields.outOfStateAmt#; 	local.memberShipName = #local.strPageFields.outOfStateTxt#; 	 break;
					}
					local.optionalDues = 0;
					if ( event.getValue('legislativeAssessment','') neq '' ){
						local.optionalDues = #local.strPageFields.legAssessmentAmt#;
					}	
					local.totalAmount = local.memberShipDues + local.optionalDues;
				</cfscript>

				<div class="BodyText">
				<div class="CPSectionTitle">MEMBERSHIP LEVEL</div>
				<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
				<tr class="frmRow1"><td class="frmText b">#local.memberShipName#:</td><td class="frmText">#dollarFormat(local.memberShipDues)#&nbsp;</td></tr>
				<cfif event.getValue('legislativeAssessment','') neq ''>
					<tr class="frmRow1"><td class="frmText b">#local.strPageFields.legAssessmentTxt#:</td><td class="frmText">#dollarFormat(local.strPageFields.legAssessmentAmt)#&nbsp;</td></tr>
				</cfif>
				<tr class="frmRow2"><td class="frmText b r">Total: &nbsp;</td><td class="frmText">#dollarFormat(local.totalAmount)#&nbsp;</td></tr>
				</table>
				<br/><br/>
				
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
						<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
						<cfloop collection="#arguments.event.getCollection()#" item="local.key">
							<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
								and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
								and left(local.key,4) neq "fld_">
								<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
							</cfif>
						</cfloop>
						<div>
							<div class="CPSection">
								<div class="CPSectionTitle">*Method of Payment</div>
								<div class="P">
									<table cellpadding="2" cellspacing="0" width="100%" border="0">
										<tr valign="top">
											<td colspan="2">Please select your preferred method of payment from the options below.</td>
										</tr>
										<tr>
											<td>
												<table cellpadding="2" cellspacing="0" width="100%" border="0">
													<tr>
														<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Credit Card</td>
													</tr>
													<tr>
														<td width="25"><input value="BD" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Checking Account</td>
													</tr>
													<tr>
														<td width="25"><input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Check by Mail</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
							</div>
								
							<div id="CCInfo" style="display:none;" class="CPSection">
								<div class="CPSectionTitle">Credit Card Information</div>
								<div class="PL PR frmText paymentGateway BT BB">
									<cfif len(local.profile_1.strPaymentForm.inputForm)>
										<div id="ccForm">#local.profile_1.strPaymentForm.inputForm#</div>
									</cfif>
								</div>
								<div class="P">
									<div class="PB">* Please confirm the statement below:</div>
									<table width="100%">
									<tr>
										<td width="25"><input name="confirmationStatementCC" id="confirmationStatementCC"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
										<td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
									</tr>
									</table>
								</div>
								<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">SUBMIT</button></div>
							</div>
								
							<div id="BDInfo" style="display:none;" class="CPSection">
								<div class="CPSectionTitle">Checking Account Information</div>
								<div class="PL PR frmText paymentGateway BT BB">
									<cfif len(local.profile_2.strPaymentForm.inputForm)>
										<div id="bdForm">#local.profile_2.strPaymentForm.inputForm#</div>
									</cfif>
								</div>
								<div class="P">
									<div class="PB">* Please confirm the statement below:</div>
									<table width="100%">
									<tr>
										<td width="25"><input name="confirmationStatementBD" id="confirmationStatementBD"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
										<td>I confirm that I have full authority to make payment from the above checking account account for my contribution.</td>
									</tr>
									</table>
								</div>
								<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">SUBMIT</button></div>
							</div>

							<div id="CheckInfo" style="display:none;" class="CPSection">
								<div class="CPSectionTitle">Check Information</div>
								<div class="P">
									Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
									<strong>Missouri Association of Trial Attorneys</strong><br />
									P.O. Box 1792<br />
									Jefferson City, Missouri 65102
								</div>
								<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button></div>
							</div>
						</div>
						<cfinclude template="/model/cfformprotect/cffp.cfm" />
						</cfform>
					</div>
				</div>
				</div>
			</cfcase>
		
			<!--- PROCESS: ====================================================================================================================================== --->
			<cfcase value="2">
				
				<!--- CHECK FOR SPAM SUBMISSION: -------------------------------------------------------------------------------- --->
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>
				
				<cfscript>
					switch(event.getValue('Membership')){
						case 'lessThan2Yrs':	local.memberShipDues = #local.strPageFields.lessThan2YrsAmt#; local.memberShipName = #local.strPageFields.lessThan2YrsText#; break;
						case 'lessThan5Yrs':	local.memberShipDues = #local.strPageFields.lessThan5YrsAmt#; local.memberShipName = #local.strPageFields.lessThan5YrsTxt#; break;
						case 'moreThan5Yrs':	local.memberShipDues = #local.strPageFields.moreThan5YrsAmt#; local.memberShipName = #local.strPageFields.moreThan5YrsTxt#; break;
						case 'sustaining':		local.memberShipDues = #local.strPageFields.sustainingAmt#; 	local.memberShipName = #local.strPageFields.sustainingTxt#; 	 break;
						case 'govt':			local.memberShipDues = #local.strPageFields.govtRateAmt#; 				local.memberShipName = #local.strPageFields.govtRateTxt#; 				 break;
						case 'outOfState':		local.memberShipDues = #local.strPageFields.outOfStateAmt#; 	local.memberShipName = #local.strPageFields.outOfStateTxt#; 	 break;
					}
										
					local.optionalDues = 0;
					if ( event.getValue('legislativeAssessment','') neq '' ){
						local.optionalDues = #local.strPageFields.legAssessmentAmt#;
					}	
					
					local.totalAmount = local.memberShipDues + local.optionalDues;
				</cfscript>

				<cfset local.timeStamp 				= now() />
				<cfset local.savedAccounting 	= false />

				<cfsavecontent variable="local.name">
					#event.getTrimValue('firstName','')# <cfif len(trim(event.getTrimValue('middleName','')))>#event.getTrimValue('middleName','')# </cfif>#event.getTrimValue('lastName','')#<cfif len(trim(event.getTrimValue('suffix','')))> #event.getTrimValue('suffix','')#</cfif>
				</cfsavecontent>
				<cfset local.ORGEmail.SUBJECT = local.ORGEmail.SUBJECT & " - From: " & trim(local.name) />
				<!--- ----------------------------- --->
				<!--- If BD, Payment and accounting --->
				<!--- ----------------------------- --->
				<cfif local.totalAmount gt 0 and event.getValue('payMeth','Check') eq "BD">
					<cfset local.strAccTemp = { totalPaymentAmount=local.totalAmount, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=arguments.event.getCollection() } >
					<cfset local.strAccTemp.payment = { detail=local.formNameDisplay, amount=local.strAccTemp.totalPaymentAmount, profileID=local.profile_2._profileID, profileCode=local.profile_2._profileCode }>
					<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
					<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
				</cfif>
				<cfset local.strData = {}>
       		 	<cfset local.strData.one = arguments.event.getCollection()/> 

				<cfsavecontent variable="local.invoice">
					#local.pageCSS#
					<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">

					<tr class="msgHeader"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
						<tr class="frmRow1"><td class="frmText b">Prefix:</td><td class="frmText">#event.getValue('prefix','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Middle Name:</td><td class="frmText">#event.getValue('middleName','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Suffix:</td><td class="frmText">#event.getValue('suffix','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Firm Name:</td><td class="frmText">#event.getValue('firmName','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Office Address:</td><td class="frmText">#event.getValue('address','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">&nbsp;</td><td class="frmText">#event.getValue('address2','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">State:</td><td class="frmText">#event.getValue('state','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">County:</td><td class="frmText">#event.getValue('county','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Phone:</td><td class="frmText">#event.getValue('phone','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Fax:</td><td class="frmText">#event.getValue('fax','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Email:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Recruited By:</td><td class="frmText">#event.getValue('recruitedBy','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Home Address:</td><td class="frmText">#event.getValue('home_Address','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">&nbsp;</td><td class="frmText">#event.getValue('home_Address2','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">City:</td><td class="frmText">#event.getValue('home_city','')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">State:</td><td class="frmText">#event.getValue('home_state','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('home_zip','')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						<tr><td colspan="2">
							<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
								<tr>
									<td class="b msgHeader">Professional License Information</td>
								</tr>				
								<tr>
									<td style="padding:6px;">
										<table cellpadding="3" border="0" cellspacing="0">						
											<tr valign="top">
												<td>
													<cfif structKeyExists(local.strData.one, "mpl_pltypeid") and listLen(local.strData.one.mpl_pltypeid)>
														<table id="state_table" cellpadding="3" border="0" cellspacing="0" style="border:1px solid ##999;border-collapse:collapse;">
															<thead>
																<tr valign="top">
																	<th align="center" class="frmText b" style="padding:6px;">State Name</th>
																	<th align="center" class="frmText b" style="padding:6px;">#local.strProfLicenseLabels.profLicenseNumberLabel#</th>
																	<th align="center" class="frmText b" style="padding:6px;">#local.strProfLicenseLabels.profLicenseDateLabel#</th>
																	<th align="center" class="frmText b" style="padding:6px;">#local.strProfLicenseLabels.profLicenseStatusLabel#</th>
																</tr>
															</thead>
															<tbody>
															<cfloop list="#local.strData.one.mpl_pltypeid#" index="local.key">
																<tr id="tr_state_#local.key#">
																	<td align="left" class="frmText" style="padding:6px;">#local.strData.one['mpl_#local.key#_licenseName']#</td>
																	<td align="center" class="frmText" style="padding:6px;">#local.strData.one['mpl_#local.key#_licenseNumber']#</td>
																	<td align="center" class="frmText" style="padding:6px;">#local.strData.one['mpl_#local.key#_activeDate']#</td>
																	<td align="center" class="frmText" style="padding:6px;">Active</td>
																</tr>
															</cfloop>
															</tbody>
														</table>
													</cfif>
												</td>
											</tr>						
										</table>
									</td>
								</tr>
							</table>						
						</td></tr>
						
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP BENEFITS</td></tr>
						<tr class="frmRow1"><td class="frmText b">Members Listserve:</td><td class="frmText l">#event.getValue('membersListserve','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Plaintiffs Listserv:</td><td class="frmText l">#event.getValue('plaintiffListserve','No')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">E-Clips:</td><td class="frmText l">#event.getValue('eclipsService','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">TrialSmith:</td><td class="frmText l">#event.getValue('trialsmith','No')#&nbsp;</td></tr>
						<tr class="frmRow1"><td class="frmText b">Emergency Reponse Team:</td><td class="frmText l">#event.getValue('emergencyResponse','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Mo-TLC:</td><td class="frmText l">#event.getValue('moTLC','No')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">Lobbyist For a Day:</td><td class="frmText l">#event.getValue('moLobbyistForADay','No')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP STATEMENTS</td></tr>
						<tr class="frmRow1"><td class="frmText b">I Agree to the membership statement:</td><td class="frmText l">#event.getValue('consentSig','I Agree')#&nbsp;#event.getValue('consentDate_new','')#&nbsp;</td></tr>
						<tr class="frmRow2"><td class="frmText b">What percentage of YOUR practice is spent representing plaintiffs?</td><td class="frmText l">#event.getValue('applicantsPlaintiffPercentage')#</td></tr>
						<tr class="frmRow1"><td class="frmText b">What percentage of YOUR FIRM's practice is spent representing plaintiffs?</td><td class="frmText l">#event.getValue('firmsPlaintiffPercentage')#</td></tr>
						<tr class="frmRow2"><td class="frmText b">If I change firms, I understand I will be required to resubmit a membership application.</td><td class="frmText l">#event.getValue('changeFirmsAcceptance')#&nbsp;#event.getValue('consentDate_new','')#&nbsp;</td></tr>
						<tr class="frmRow1">
							<td class="frmText b">#local.strPageFields.MataMembershipWaiver#</td>
							<cfif arguments.event.getValue('mataMembershipWaiver',0) EQ 1>
								<td class="frmText l">Yes</td>
							<cfelse>
								<td class="frmText l">No</td>
							</cfif>
						</tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP LEVEL</td></tr>
						<tr class="frmRow1"><td class="frmText b">#local.memberShipName#:</td><td class="frmText">#dollarFormat(local.memberShipDues)#&nbsp;</td></tr>
						<cfif event.getValue('legislativeAssessment','') neq ''>
							<tr class="frmRow1"><td class="frmText b">#local.strPageFields.legAssessmentTxt#:</td><td class="frmText">#dollarFormat(local.strPageFields.legAssessmentAmt)#&nbsp;</td></tr>
						</cfif>
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<tr class="msgHeader"><td colspan="2" class="b">TOTAL AMOUNT</td></tr>
						<tr class="frmRow1"><td class="frmText b">Amount:</td><td class="frmText">#dollarFormat(local.totalAmount)#&nbsp;</td></tr>
						<cfif local.totalAmount gt 0>
							<tr class="frmRow1"><td class="frmText b">Pay Method:</td><td class="frmText">
								<cfif event.getValue('payMeth','Check') eq "CC">
									Credit Card
									<cfset arguments.event.setValue('p_#local.profile_1._profileID#_mppid',int(val(arguments.event.getValue('p_#local.profile_1._profileID#_mppid',0)))) />
									<cfif arguments.event.getValue('p_#local.profile_1._profileID#_mppid') gt 0>
										<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
												mppid     = arguments.event.getValue('p_#local.profile_1._profileID#_mppid'),
												memberID  = val(local.useMID),
												profileID = local.profile_1._profileID) />
										- #local.qrySavedInfoOnFile.detail#
									</cfif>
								<cfelseif event.getValue('payMeth','Check') eq "BD">
									Checking Account
									<cfset arguments.event.setValue('p_#local.profile_2._profileID#_mppid',int(val(arguments.event.getValue('p_#local.profile_2._profileID#_mppid',0)))) />
									<cfif arguments.event.getValue('p_#local.profile_2._profileID#_mppid') gt 0>
										<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
												mppid     = arguments.event.getValue('p_#local.profile_2._profileID#_mppid'),
												memberID  = val(local.useMID),
												profileID = local.profile_2._profileID) />
										- #local.qrySavedInfoOnFile.detail#
									</cfif>
								<cfelse>
									Check by Mail
								</cfif>
								&nbsp;</td>
							</tr>
						</cfif>
					</table>
				</cfsavecontent>

				<cfset local.qryStates = application.objCommon.getStates()>

				<cfif(len(trim(arguments.event.getValue('state',''))))>
					<cfquery name="local.getStateID" dbtype="query">
						select stateID from [local].qryStates where StateCode = <cfqueryparam  value="#arguments.event.getValue('state','')#" cfsqltype="cf_sql_varchar">
					</cfquery>
					<cfset local.thisStateID = val(local.getStateID.stateID)>
				<cfelse>
					<cfset local.thisStateID = 0>
				</cfif>	

				<cfif(len(trim(arguments.event.getValue('home_state',''))))>
					<cfquery name="variables.getStateID" dbtype="query">
						select stateID from [local].qryStates where StateCode = <cfqueryparam  value="#arguments.event.getValue('home_state','')#" cfsqltype="cf_sql_varchar">
					</cfquery>
					<cfset local.homeStateID = val(variables.getStateID.stateID)>
				<cfelse>
					<cfset local.homeStateID = 0>
				</cfif>	

				<cfswitch expression="#arguments.event.getValue('Membership','')#">
					<cfcase value="lessThan2Yrs,lessThan5Yrs,moreThan5Yrs,sustaining">
						<cfset local.contactTypeColumnValue = 'Attorney' />
					</cfcase>
					<cfcase value="govt">
						<cfset local.contactTypeColumnValue = 'Government' />
					</cfcase>
					<cfcase value="outOfState">
						<cfset local.contactTypeColumnValue = 'Out of State' />
					</cfcase>
				</cfswitch>

				<cfset local.getContactTypeColumnValueIDs = application.objCustomPageUtils.getCustomFieldData(columnName='Contact Type',orgID=local.orgID,columnValueList=local.contactTypeColumnValue)>
				<cfset local.contactTypeValueIDlist = valuelist(local.getContactTypeColumnValueIDs.valueID)>

				<cfscript>
					local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid'));
					
					local.objSaveMember.setAddress(type = 'Address', address1 = arguments.event.getValue('Address',''), 
						address2 = arguments.event.getValue('Address2',''), city = arguments.event.getValue('City',''),
						county = arguments.event.getValue('County',''), stateID = local.thisStateID, postalCode = arguments.event.getValue('Zip',''));
					local.objSaveMember.setAddress(type = 'Home Address', address1 = arguments.event.getValue('home_Address',''), 
						address2 = arguments.event.getValue('home_Address2',''), city = arguments.event.getValue('home_City',''), 
						stateID = local.homeStateID, postalCode = arguments.event.getValue('home_Zip',''));
					local.objSaveMember.setPhone(addressType = 'Address', Type = 'phone', value = arguments.event.getValue('phone',''));
					local.objSaveMember.setPhone(addressType = 'Address', Type = 'fax', value = arguments.event.getValue('fax',''));
					local.objSaveMember.setEmail(Type =	'email', value = arguments.Event.getValue('email',''));
					local.objSaveMember.setCustomField(field = 'Contact Type', valueID = local.contactTypeValueIDlist);
					if (arguments.event.getValue('membersListserve','No') eq 'Yes') {
						local.objSaveMember.setCustomField(field = 'Members Listserve', value = 1);
					}
					if (arguments.event.getValue('plaintiffListserve','No') eq 'Yes') {
						local.objSaveMember.setCustomField(field = 'Plaintiff Listserve', value = 1);
					}
					if (arguments.event.getValue('emergencyResponse','No') eq 'Yes') {
						local.objSaveMember.setCustomField(field = 'Emergency Response Volunteer', value = 1);
					}
					if (arguments.event.getValue('eclipsService','No') eq 'Yes') {
						local.objSaveMember.setCustomField(field = 'Eclips Service', value = 1);
					}
					if (arguments.event.getValue('moTLC','No') eq 'Yes') {
						local.objSaveMember.setCustomField(field = 'MoTLC', value = 1);
					}
					if (arguments.event.getValue('moLobbyistForADay','No') eq 'Yes') {
						local.objSaveMember.setCustomField(field = 'moLobbyistForADay', value = 1);
					}
					local.objSaveMember.setCustomField(field = 'Applicant Plaintiff Percentage', value = arguments.event.getValue('applicantsPlaintiffPercentage'));
					local.objSaveMember.setCustomField(field = 'Firms Plaintiff Percentage', value = arguments.event.getValue('firmsPlaintiffPercentage'));
					local.objSaveMember.setCustomField(field = 'Change Firms Acceptance', value = arguments.event.getValue('changeFirmsAcceptance'));
					local.objSaveMember.setCustomField(field = 'MATA Membership Waiver', value = arguments.event.getValue('mataMembershipWaiver',0));
					local.objSaveMember.setMemberType(memberType = 'User');
				</cfscript>
				<cfset local.newMemberNumber = "">
				<cfset local.isMissouriNumberSet = false>
				<cfset local.isMissouriNumber = "">
				<cfset local.licensesNames = ArrayNew(1)>
				<cfset local.qryOrgPlTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.orgID)>
				<cfloop query="local.qryOrgPlTypes">
					<cfset local.licensesNames[local.qryOrgPlTypes.PLTypeID] = local.qryOrgPlTypes.PLName>	
				</cfloop>

				<cfloop list="#event.getValue('mpl_pltypeid')#" index="local.thisItem">
					<cfset local.thisNumber = left(event.getValue("mpl_#local.thisItem#_licenseNumber",""),200)>
					<cfset local.thisDate = event.getValue("mpl_#local.thisItem#_activeDate","")>
					<cfset local.thisStatus = event.getValue("mpl_#local.thisItem#_status","")>
					<cfset  local.objSaveMember.setProLicense(name=local.licensesNames[local.thisItem],status=local.thisStatus,license=local.thisNumber, date=local.thisDate)>
					<cfif local.licensesNames[local.thisItem] EQ 'Missouri' AND len(local.thisNumber)>
						<cfset local.isMissouriNumberSet = true>
						<cfset local.isMissouriNumber = local.thisNumber/>
					</cfif>
				</cfloop>

				<cfif arguments.event.getTrimValue('isNewRecord',"false") eq "true" and local.isMissouriNumberSet>
					<cfset local.strCheckMemberNumber = CreateObject("component","model.admin.members.members").checkMemberNumber(mcproxy_orgID=local.orgID, memberID=arguments.event.getTrimValue('memberid'), memberNumber=trim(local.isMissouriNumber))>						
					<cfif local.strCheckMemberNumber.success is not 1>
						<cfset local.newMemberNumber = IIf((Left(local.isMissouriNumber,2) NEQ 'MO'), DE('MO' & local.isMissouriNumber) , DE(local.isMissouriNumber))>
					<cfelse>
						<cfset local.newMemberNumber = trim(local.isMissouriNumber)>
					</cfif>					
				</cfif>		

				<cfset local.strCheckMemberNumber = CreateObject("component","model.admin.members.members").checkMemberNumber(mcproxy_orgID=local.orgID, memberID=arguments.event.getTrimValue('memberid'), memberNumber=local.newMemberNumber)>						
	
				<cfif local.strCheckMemberNumber.success is 1 and len(local.newMemberNumber)>
					<cfset local.objSaveMember.setDemo(membernumber=local.newMemberNumber)>
				</cfif>				

				<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)/>
								
				<!--- email member ---------------------------------------------------------------------------------------------- --->

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
						<hr />
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=local.memberEmail.from },
                            emailto=[{ name="", email=local.memberEmail.to }],
                            emailreplyto=local.ORGEmail.to,
                            emailsubject=local.memberEmail.SUBJECT,
                            emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & local.formNameDisplay,
                            emailhtmlcontent=local.mailContent,
                            siteID=local.siteID,
                            memberID=val(arguments.event.getTrimValue('memberid')),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>

				<cfset local.emailSentToUser = local.responseStruct.success>
				
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							#local.name# was not sent email confirmation due to bad Data.<br />
							Please contact, and let them know.
							<hr />
						</cfif>
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(local.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.ORGEmail.from},
					emailto=local.arrEmailTo,
					emailreplyto=local.ORGEmail.from,
					emailsubject=local.ORGEmail.subject,
					emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " &  local.formNameDisplay,
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>
				
				<!--- create pdf and put on member's record --->
				<cfset local.uid = createuuid()>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.strData.one.mc_siteinfo.sitecode)>
				<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
						<html>
						<head>
						<style>

						</style>
						</head>
						<body>
							<p>Thank you for your application.</p>	
							<hr />
							<p>Here are the details of your application:</p>
							#local.invoice#
						</body>
						</html>
					</cfoutput>
				</cfdocument>
				<cfset local.strPDF = structNew()>
				<cfset local.currentDate = dateTimeFormat(now())>
				<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
				<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(local.currentDate,'m-d-yyyy')#.pdf">
				<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
				<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(local.currentDate,'hhmmss')#/#getTickCount()#tr!@l")>
				<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
				<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=val(arguments.event.getTrimValue('memberid')),strPDF=local.strPDF,siteID=local.siteID)>
				<!--- relocate to message page --->
				<cfset session.invoice = local.invoice />

				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			
			<!--- MESSAGE AFTER THE POST: ======================================================================================================================= --->
			<cfcase value="99">
				<!--- output to screen --->
				#local.strPageFields.ConfirmationMessage#
				<br />
				<cfif isDefined("session.invoice")>
					<div class="BodyText">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
					<cfset session.invoice = "" />
				</cfif>
			</cfcase>
			
			<!--- SPAM MESSAGE: ================================================================================================================================= --->
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
			
		</cfswitch>
	</div>
</cfoutput>
