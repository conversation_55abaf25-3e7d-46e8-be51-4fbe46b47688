<cfsavecontent variable="local.pageJS">
	<cfoutput>
         <style>
            .exportPageWrap{
                .superuser.small{
                    font-size: 60% !important;
                }
            }
        </style>
		<script language="javascript">
			function exportCSV(){
                var kw = $('##kw').val();
                var shNP = $('##shNP').val();
                var ce = $('##ce').val();
                var cs = $('##cs').val();
                var me = $('##me').val();
                var ms = $('##ms').val();
                var pt = $('##pt').val() || '';
                var ps = $('##ps').val() || '';
                self.location.href = '#this.link.exportCSV#&kw='+kw+'&shNP='+shNP+'&ce='+ce+'&cs='+cs+'&me='+me+'&ms='+ms+'&pt='+pt+'&ps='+ps;
            }
			function exportPermission(){
                var kw = $('##kw').val();
                var shNP = $('##shNP').val();
                var ce = $('##ce').val();
                var cs = $('##cs').val();
                var me = $('##me').val();
                var ms = $('##ms').val();
                var pt = $('##pt').val() || '';
                var ps = $('##ps').val() || '';
                self.location.href = '#this.link.exportPermission#&kw='+kw+'&shNP='+shNP+'&ce='+ce+'&cs='+cs+'&me='+me+'&ms='+ms+'&pt='+pt+'&ps='+ps;
            }
            function exportJSON(){
                var objParams = {
					kw: $('##kw').val(),
					shNP: $('##shNP').val(),
					ce: $('##ce').val(),
					cs: $('##cs').val(),
					me: $('##me').val(),
					ms: $('##ms').val(),
					pt: $('##pt').val(),
					ps: $('##ps').val(),
					siteID: $('##siteID').val()
				};
                var chkResultexportJSON = function (r) {
                    debugger;
                    if (r.success && r.success.toLowerCase() == 'true') {
                        $('.exportMsg').removeClass('d-none');
                    }else {
                        $('.exportMsg').addClass('d-none');                         
                    }
                };
				TS_AJX('PAGE', 'exportJSON', objParams, chkResultexportJSON, chkResultexportJSON, 10000, chkResultexportJSON);
            }

			$(function() {
				
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">
<cfoutput>
    <cfloop array="#local.arrKeysToPopulate#" index="local.key" item ="local.item">
        <cfif (structKeyExists(local.rc, "#local.item#")) >
			<input type="hidden" id="#local.item#" name="#local.item#" value="#local.rc['#local.item#']#">
		<cfelse>
			<input type="hidden" id="#local.item#" name="#local.item#" value="">			
		</cfif>
        
    </cfloop>
    <input type="hidden" id="siteID" name="siteID" value="#local.siteID#">
    <input type="hidden" id="memberID" name="memberID" value="#session.cfcuser.memberdata.memberID#">
    <div class="pl-2 exportPageWrap">
        <div id="pills-import" class="tab-pane fade active show">
            <div class="card card-box mt-2">
               <div class="card-header py-1 bg-light">
                  <div class="card-header--title font-weight-bold font-size-md">Download Pages CSV</div>
               </div>
               <div class="card-body pb-3">
                  <div>
                     <div class="mb-3">This download produces a CSV of the filtered pages with one row per page. Columns include the section, page, status, and various page settings.</div>
                     <button type="button" class="btn btn-sm btn-primary" onclick="exportCSV();">
                         <span class="btn-wrapper--icon">
                             <i class="fa-light fa-file-export"></i>
                         </span>
                         <span class="btn-wrapper--label">Download</span>
                     </button>
                  </div>
               </div>
            </div>
        </div>

        <cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
             <div id="pills-import" class="tab-pane fade active show">
                <div class="card card-box mt-2">
                   <div class="card-header py-1 bg-light">
                      <div class="card-header--title font-weight-bold font-size-md">Download Pages JSON Files <span class="superuser small"></span></div>
                   </div>
                   <div class="card-body pb-3">
                      <div>
                        <div class="card card-box p-3 mb-2 alert alert-warning d-none exportMsg">
                            The export process is running in the background.<br/>
                            You will receive an e-mail with an attachment of pages once the export is complete.
                        </div>
                         <div class="mb-3">This download produces a ZIP of necessary JSON files of the filtered pages to import onto development, beta, or production.</div>
                         <button type="button" class="btn btn-sm btn-primary" onclick="exportJSON();">
                             <span class="btn-wrapper--icon">
                                 <i class="fa-light fa-file-export"></i>
                             </span>
                             <span class="btn-wrapper--label">Download</span>
                         </button>
                      </div>
                   </div>
                </div>
             </div>
        </cfif>
        
        <div id="pills-import" class="tab-pane fade active show">
            <div class="card card-box mt-2">
                <div class="card-header py-1 bg-light">
                    <div class="card-header--title font-weight-bold font-size-md">Download Page Permissions</div>
                </div>
                <div class="card-body pb-3">
                    <div>
                    <div class="mb-3">This download produces a CSV of the filtered pages including any groups that have View permission applied to those pages.</div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="exportPermission();">
                        <span class="btn-wrapper--icon">
                            <i class="fa-light fa-file-export"></i>
                        </span>
                        <span class="btn-wrapper--label">Download</span>
                    </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</cfoutput>