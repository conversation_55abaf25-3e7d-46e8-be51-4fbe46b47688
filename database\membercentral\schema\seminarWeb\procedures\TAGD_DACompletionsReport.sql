ALTER PROC dbo.TAGD_DACompletionsReport
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
	SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0)
	SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0)

	-- drop if exists
	IF OBJECT_ID('tempdb..##tmpSWCreditCompletions') IS NOT NULL 
		DROP TABLE ##tmpSWCreditCompletions

	SELECT dbo.fn_csvSafeString(d.LastName) as [Last Name]
		, dbo.fn_csvSafeString(d.FirstName) as [First Name]
		, dbo.fn_csvSafeString(d.Email) as [Email]
		, dbo.fn_csvSafeString(e.dateEnrolled) as [Enrollment Date]
		, dbo.fn_csvSafeString(e.datecompleted) as [Completion Date]
		, CASE WHEN s.seminarid <> 733 THEN NULL 
		ELSE (SELECT dbo.fn_csvSafeString(MAX(passingPct))
				FROM formbuilder.dbo.tblResponses
				WHERE depomemberdataid = d.depomemberdataID 
				AND formID = 13 
				AND isActive = 1) 
		END AS [Exam Score]
		, CASE WHEN s.seminarid <> 733 THEN NULL ELSE (SELECT dbo.fn_csvSafeString(count(sfrid))
				FROM tblSeminarsAndFormResponses
				WHERE enrollmentid = e.enrollmentid 
				AND seminarformID = 7) 
		END AS [Total Tries]
	INTO ##tmpSWCreditCompletions
	FROM dbo.tblEnrollments AS e 
		INNER JOIN dbo.tblSeminars AS s ON e.seminarID = s.seminarID 
		INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
	WHERE e.seminarID = 733
		AND e.isActive = 1 
		AND e.dateEnrolled BETWEEN @startdate and @enddate
		AND e.datecompleted IS NOT NULL
		AND (d.AdminFlag2 IS NULL OR d.AdminFlag2 <> 'Y')
	ORDER BY d.LastName, d.FirstName, e.enrollmentID

	-- export data
	DECLARE @cmd varchar(6000)
	declare @tmpBCP TABLE (theoutput varchar(max))
	set @cmd = 'bcp ##tmpSWCreditCompletions out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(40))
	insert into @tmpBCP (theoutput)
	exec master..xp_cmdshell @cmd	

	-- return count of records
	SELECT count(*) AS returnCount
	FROM ##tmpSWCreditCompletions

	-- get fields returned
	EXEC tempdb.dbo.SP_COLUMNS ##tmpSWCreditCompletions

	-- drop temp table
	IF OBJECT_ID('tempdb..##tmpSWCreditCompletions') IS NOT NULL 
		DROP TABLE ##tmpSWCreditCompletions

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
