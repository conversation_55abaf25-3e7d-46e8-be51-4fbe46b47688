<cfoutput>
<cfsavecontent variable="local.pageJS">
	<script language="javascript">
		let classificationTable;

		function initClassificationTable() {
			classificationTable = $('##classificationTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"info": false,
				"scrollY": "450px",
				"scrollCollapse": true,
				"language": {
					"emptyTable": "No Classifications Found."
				},
				"ajax": {
					"url": "#local.classificationListJSONLink#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": "classificationname", "width": "80%" },
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="editClassification('+data.classificationid+');return false;" title="Edit Classification"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" id="btnDel'+data.classificationid+'" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="removeClassification('+data.classificationid+');return false;" title="Delete Classification"><i class="fa-solid fa-trash-can"></i></a>';
								renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1 classificationmoveup'+(!data.canmoveup ? ' invisible' : '')+'" title="Move Classification Up" onclick="moveClassification('+data.classificationid+',\'up\');return false;"><i class="fa-solid fa-arrow-up"></i></a>';
								renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1 classificationmovedown'+(!data.canmovedown ? ' invisible' : '')+'" title="Move Classification Down" onclick="moveClassification('+data.classificationid+',\'down\');return false;"><i class="fa-solid fa-arrow-down"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "text-center",
						"width": "20%"
					}
				],
				"searching": false,
				"ordering": false
			});
		}
		function reloadClassificationTable() {
			classificationTable.draw();
		}
		function editClassification(cID){
			MCModalUtils.showModal({
				isslideout: true,
				size: 'md',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: (cID > 0 ? 'Edit' : 'Add') + ' Classification ',
				iframe: true,
				contenturl: '#this.link.editClassification#&classificationID=' + cID,
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.validateClassification',
					extrabuttonlabel: 'Save Details',
				}
			});
		}
		function removeClassification(cID) {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadClassificationTable();
				} else {
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					alert('We were unable to delete this Classification.');
				}
			};
			let delBtn = $('##btnDel'+cID);
			mca_initConfirmButton(delBtn, function(){
				var objParams = { classificationID:cID };
				TS_AJX('ADMINREFERRALS','deleteClassification',objParams,removeResult,removeResult,10000,removeResult);
			});
		}
		function moveClassification(cid, dir) {
			var moveItem = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){classificationTable.draw(false);}
				else alert('We were unable to move '+ dir +' this classification.');
			};
			var objParams = { classificationID:cid, dir:dir };
			TS_AJX('ADMINREFERRALS','doClassificationMove',objParams,moveItem,moveItem,10000,moveItem);
		}
		$(function() {
			initClassificationTable();
		});
	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<div class="card mb-1">
	<div class="card-header bg-light p-1">
		<div class="card-header--title font-size-lg ml-2">Classifications</div>
		<button name="btnAddClassification"  id="btnAddClassification" onClick="editClassification(0);" type="button" class="btn btn-sm btn-secondary mr-1" title="Add Classification"><i class="fa-regular fa-circle-plus"></i></button>
	</div>
	<div class="card-body p-1">
		<table id="classificationTable" class="table table-sm table-striped table-bordered" style="width:100%">
			<thead>
				<tr>
					<th>Classifications</th>
					<th>Actions</th>
				</tr>
			</thead>
		</table>
	</div>
</div>
</cfoutput>