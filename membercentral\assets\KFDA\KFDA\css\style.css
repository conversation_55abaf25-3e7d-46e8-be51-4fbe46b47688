body{ 
  background:#fff !important;
  color: #333333; 
  font-family: 'Open Sans', sans-serif !important;  
  font-size: 16px; /*line-height: 1.6; */ 
  font-weight:400;
}
body.body{background:#fff !important;}

/***********
header
************/

.header-box{ background:#fff;}
.logo-box{ padding:10px 0; background:rgba(0, 0, 0, 0) url("../images/header-pattern.jpg") no-repeat scroll 0 0; background-size:cover; position:relative;}
.logo-box .logo-img {
  display: block;
  padding-left: 40px;
  position: relative;
}
.logo-box:after {background: #265997; bottom: 5px;content: "";height: 2px;left: 0;position: absolute;right: 0;width: 100%;}
.navigation{ background:#fff; text-align:center; margin-top:18px;}
.navigation .navbar-inner{background:none; border:0; border-radius:0; box-shadow:none;}
.navigation .navbar ul.nav{ margin:0; padding:0; float:none;}
.navigation ul li{ list-style:none; display:inline-block; float:none !important; vertical-align:top; position:relative;}
.navigation .navbar .nav li a{ 
  padding:8px 10px;  
  color: #666; 
  display: block; 
  position: relative; 

  font-size:80%;
  font-weight:bold;
  letter-spacing: 0; 
  text-transform: uppercase; 
  text-shadow:none;
}
.navigation .navbar .nav li a:hover, .navigation ul>li a:focus,.navigation .navbar .nav > li:hover a{ 
    background:#999999; 
    color:#fff; 
    border-radius:10px; 
    border-bottom-right-radius:0; 
    border-bottom-left-radius:0;
}

.navigation ul li ul {
  background: #999999 none repeat scroll 0 0;
  display: none;
  left: -50%;
  position: absolute;
  top: 100%;
  margin: 0;
  padding:0 0 8px 0;
  min-width:250px;
  border-radius:10px !important;
  -webkit-border-radius:8px !important;
  -moz-border-radius:8px !important;  
  box-shadow:none; 
  border:0;
  z-index:999;
  overflow:hidden;
}

.navigation ul li:nth-child(3) ul {
  left: -13%;
}

.navbar .nav > li > .dropdown-menu::before,.navbar .nav > li > .dropdown-menu::after{display:none;}
.navigation .navbar .nav li ul li{display:block; border-bottom: 1px solid #fff;  padding: 0 10px; text-align: left;}
.navigation .navbar .nav li ul li a {  
  border-radius: 0 !important;
  color: #fff;
  font-size: 12px;
  padding: 6px 0;
  text-align: left;
  text-transform: none;
  font-size:70%;
  font-weight:normal;  
  display:inline-block;
  background:none !important;
}
.navigation .navbar .nav li ul li:hover {background:#00589D !important; }
.navigation .navbar .nav li ul li:last-child{ border-bottom: #999999 !important;}
.navigation ul li ul li:hover a{border-radius:0;}
.navigation ul li:hover ul {display:block; border-radius:10px; margin:0; border-top-left-radius:0; }
.navigation ul li ul li{ display:block;}
.navigation ul li a {font-family: 'Open Sans', sans-serif !important; text-decoration:none !important;}

/************
content
*************/
.content{background:#fff;}
.inner-content-box{ padding:40px; }
.page-title{  color: #0e568d; font-family: Verdana,Arial,Helvetica,sans-serif; font-size: 11.5pt; font-weight: bold; margin:0 0 20px;}

.list-box{ margin-bottom:30px;}
.list-box:last-child{ margin-bottom:10px;}
.list-title{background-color: #fff; color: #0e568d; font-family: Verdana,Arial,Helvetica,sans-serif;  font-size: 10pt; font-weight: bold; margin:0 0 10px; }
.list-box{ font-family: Verdana,Arial,Helvetica,sans-serif;  color: #666; font-size: 9pt;}
.list-box li{ margin-bottom:10px; list-style:none;}
.list-box a{ color:#2d85c8; text-decoration:underline;}

.login-box{ width:35%; border:1px solid #7D7D7D; padding-bottom:20px;}
.login-title{  background-color: #fff; color: #0e568d; line-height:16px; font-family: Verdana,Arial,Helvetica,sans-serif;font-size: 12px;overflow-wrap: break-word; padding-left: 10px; width: 95%; font-weight: bold; margin:-7px auto 20px;text-overflow: ellipsis; height: 16px;  white-space: nowrap; overflow:hidden;}
.login-box li{ font-family: Verdana,Arial,Helvetica,sans-serif;  color: #666; font-size: 9pt; margin:0 0 10px;}
.login-box ul{position:relative; padding-top:20px;}
.login-box ul::after {
  background: #7D7D7D none repeat scroll 0 0;
  top: 0;
  content: "";
  height: 1px;
  left: 0;
  position: absolute;
  right: 30px;

  z-index: 9999;
}
.button{ text-align:center;} 
.button-box{ text-align:center;}
.tsAppBodyText{margin:0;}

/************
footer
*************/
.footer-box{ background:#fff; padding:15px; text-align:center;}
.footer-box > .container{ border-top:1px solid #7D7D7D; padding-top:10px;}
.footer-text{  color: #666; font-family: Verdana,Arial,Helvetica,sans-serif;  font-size: 9pt;}
.footer-logo{ display:inline-block; vertical-align:top; margin-right:20px;}
.footer-text{ display:inline-block; vertical-align:top;}


@media (max-width:1270px){
.navigation .navbar .nav li a {
  font-size: 14px;
  letter-spacing: 0.6px;
  padding: 15px 7px;
  margin:0;
}
.container{width:100%;}
.logo-box{padding-left:40px;}
}

@media (max-width:1023px){
	.wrapper{display:block !important; }
	.navigation{background: #cdcdcd ;}
.navigation .navbar .btn-navbar:focus{outline:none;}
.navigation .navbar .btn-navbar .icon-bar{display:none;}
.navigation .navbar .btn-navbar {
  background: #cdcdcd ;
  border: 0 none;
  color: #333;
  float: none;
  font-size: 16px;
  font-weight: bold;
  line-height: 57px;
  margin: 0;
  padding: 0;
  text-shadow: none;
  width: 100%;
  border-radius:0;
}
.navigation ul li{display:block;}
.navbar .nav li.dropdown.active > .dropdown-toggle, .navbar .nav>li.dropdown.open.active > .dropdown-toggle, .navbar .nav li.dropdown.open > .dropdown-toggle{background:#999999; color:#fff;}
.navigation ul li ul{width:100%; text-align:center; position:static;} 
.navigation .navbar .nav li ul li a{text-align:center;}
.navigation  .nav-collapse .dropdown-menu{background:#999;}
.content-box-left{
  float: none;
  width: 100%;
  margin-bottom:40px;
}
.login-box{
  float: none;
  width: 100%;
}
.navigation .navbar-inner{padding:0;}
.inner-content-box{padding:20px;}
.logo-box{padding-left:20px;}
.navigation .collapse.in{background:#cdcdcd}
.navigation ul li:hover ul{border-top-right-radius:0;}
	}
	
.contentWrapper{padding:25px;}
#masterContentArea{min-height:250px;}

.logOutLink {
    float: right;
    text-align: right;
	text-decoration:underline !important;
}
.container{
  max-width: 1170px;
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.container
 {
  min-width: 970px;
}
.container:before, .container:after {
  content: " ";
  display: table;
}
.container:after {
  clear: both;
}
.container{width:100%;}

#masterContentArea {
  padding-top: 10px; 
  background-color: white !important;
  height: 100%;
}

.wrapper {
  display: table;
  width: 100%;
  height: 100%;
}
#masterContentArea { line-height: 25px !important; }
