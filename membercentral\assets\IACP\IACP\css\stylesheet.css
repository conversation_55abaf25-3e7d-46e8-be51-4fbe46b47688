@charset "utf-8";
html{min-height: 100%;}
body { color: #777777; font-family: 'Poppins', sans-serif; font-size: 14px; font-weight: 300; line-height: 1.625em; position: relative; padding-bottom: 280px;min-height: 100vh;}
*, input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
.content input[type=text],.content input[type=password]{ -moz-box-sizing: initial; -ms-box-sizing: initial; -o-box-sizing: initial; -webkit-box-sizing: initial; box-sizing: initial;}
img { max-width: 100%; }
.wrapper a:focus { text-decoration: none; outline: none; }
a { color: #b62025; text-decoration: underline; }
a:hover .BodyText, .BodyText a:hover, a:hover, a:focus { color: #0056b3; }
.container { padding: 0 15px; }
.xsHidden { display: block !important; }
.xsVisible { display: none !important; }
/***Header***/
#header.header .navbar { margin-bottom: 0; }
#header.header { position: relative; }
.logoBox { position: relative; z-index: 9999; background: #fff; margin-bottom: 0; padding: 5px 0px 27px; }
.logoBox .brand img { max-width: 155px; }
.logoBox .brand img.xsHidden { display: inline-block !important; }
.logoBox ul { float: right; list-style: none; margin: 0px; }
.logoBox ul li a { line-height: 50px; height: 50px; text-transform: capitalize; color: #283c58; font-family: 'Roboto', sans-serif; font-weight: 400; }
#header .navbar .container { width: 1170px; padding: 0 15px; }
#header.header .navbar-inner { background: rgba(13, 13, 13, 0.8); border: none; -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; padding: 0; min-height: inherit; }
#header.header a { text-decoration: none; }
#header .navbar .brand { padding: 0; margin: 0; }
#header.header .navbar .nav li a { font-size: 20px; padding: 14px 20px 7px; text-align: center; text-decoration: none; text-transform: uppercase; -moz-text-shadow: none; -ms-text-shadow: none; -o-text-shadow: none; -webkit-text-shadow: none; text-shadow: none; -moz-transition: all ease 0.5s; -ms-transition: all ease 0.5s; -o-transition: all ease 0.5s; -webkit-transition: all ease 0.5s; transition: all ease 0.5s; font-family: 'Roboto', sans-serif; color: #ffffff; font-weight: normal; letter-spacing: 1px; text-transform: capitalize; position: relative; z-index: 99999; margin-bottom: 7px; background: #3d3d3d; }
#header .navbar .nav li.dropdown > ul.dropdown-menu > li:hover > a, #header.header .navbar .nav li.dropdown > ul.dropdown-menu > li > ul.dropdown-submenu > li:hover > a, header .navbar .nav li.dropdown > ul.dropdown-menu > li:focus > a, #header.header .navbar .nav li.dropdown > ul.dropdown-menu > li > ul.dropdown-submenu > li:focus > a { opacity: 0.8; }
#header.header .navbar .nav li:last-child a { border-right: none; }
#header.header .navbar .nav li.active a { background: #00A3E0; color: #FFFFFF; }
#header.header .navbar .nav li a:hover, #header.header .navbar .nav li a:focus, .footer-menu li a:hover, .footer-menu li a:focus { }
#header.header .nav-collapse .nav { margin: 0; }
.navIcon .menu { display: none; }
#header.header .navbar .nav > li > .dropdown-menu::after, #header.header .navbar .nav > li > .dropdown-menu::before { display: none; }
#header.header .navbar .pull-right > li > .dropdown-menu, #header.header .navbar .nav > li > .dropdown-menu { left: 0; right: inherit; top: 34px; }
.dropdown-menu > li > a { background: rgb(81,121,146); }
#header.header .navbar .nav li .dropdown-menu > li > a { padding: 7px 10px; border-bottom: 1px solid #6699cc; font-size: 11px; line-height: 16px; border-right: none; text-align: left; white-space: normal; }
#header.header .dropdown-menu { -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; }
.dropdown-menu { width: 195px; transition: top 0.45s cubic-bezier(0.165, 0.84, 0.44, 1) 0s; }
#header .navbar .nav > li > a { position: relative; z-index: 99999; }
.dropdown-submenu > .dropdown-menu { border: none; padding: 0; margin: 0; }
.dropdown-submenu > a::after { display: none; }
#header.header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a { border: none; background: rgb(25,25,25); border-bottom: 1px solid #303030; }
#header.header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a:hover, #header.header .navbar .nav li a:hover, #header.header .navbar .nav li a:focus, .navbar .nav li.dropdown.open > .dropdown-toggle, .navbar .nav li.dropdown.active > .dropdown-toggle, .navbar .nav li.dropdown.open.active > .dropdown-toggle, .dropdown:hover .dropdown-toggle { color: #fff!important; }
[data-toggle="dropdown"] {
display: none;
}
.dropdown-menu { border-radius: 0; background: rgb(0, 107, 182); }
.navMain { height: auto; }
#header.header .navbar .nav li.dropdown ul.dropdown-menu { background-color: rgba(0,0,0,0.8); padding: 6px 1px; }
#header.header .navbar .nav li.dropdown ul.dropdown-menu li { padding: 4px 15px; margin: 0; word-break: break-all; }
#header.header .navbar .nav li.dropdown ul li a { font-size: 16px; font-weight: 400; padding: 0px; text-transform: none; border-bottom: 1px solid rgba(255, 255, 255, 0.42); background: transparent; font-family: 'Roboto', sans-serif; white-space: nowrap; color: #fefefe; line-height: 1.42857143; padding-bottom: 1px; margin-bottom: 6px; letter-spacing: 0.1px; }
#header.header .navbar .nav li.dropdown.multi-ul > ul > li > a { font-size: 16px; font-weight: 700; letter-spacing: 1px; text-transform: uppercase; padding-bottom: 5px; margin-bottom: 10px; word-wrap: normal; }
#header.header .navbar .nav li.dropdown.multi-ul ul { min-width: 499px; }
#header.header .navbar .nav li.dropdown.multi-ul ul li > ul { list-style: none; margin: 0; }
#header.header .navbar .nav li.dropdown.multi-ul ul li > ul li { width: 50%; display: inline-block; padding-left: 0; margin: 0 -2px; }
#header.header .navbar .nav li.dropdown.multi-ul > ul > li > ul li > a { border-bottom: 0px; font-weight: normal; margin-bottom: 5px; display: inline-block; vertical-align: top; }
/******Footer*****/
.footer { background: #333333; padding: 16px 0 18px; position: absolute; bottom: 0px; width: 100%; left: 0; right: 0; }
.footer .footCol { padding: 0 15px; margin: 0px; }
.footer .span3 { width: 25%; }
.footer .span6 { width: 50%; }
.footer ul { list-style: none; }
.footer-top p, .footer-top a { margin: 0; font-size: 12px; color: #fff; text-decoration: none; font-family: 'Roboto', sans-serif; }
.footerHead { padding-bottom: .5em; color: #fff !important; font-size: 12px; border-bottom: 1px solid #fff; line-height: 1.2em; margin-top: 0; font-weight: 600; margin-bottom: 1rem; }
.socialLinks ul { margin: 0px; }
.socialLinks ul li { display: inline-block; margin-right: 0.5em; background: #111111; width: 40px; height: 40px; display: inline-table; text-align: center; -webkit-transition: all 0.3s ease 0s; -moz-transition: all 0.3s ease 0s; -o-transition: all 0.3s ease 0s; transition: all 0.3s ease 0s; padding: 4px; }
.socialLinks ul li a { display: inline-block; width: 100%; height: 100%; opacity: 0.9; }
.socialLinks ul li:hover a { opacity: 1; }
.socialLinks ul li:hover { background: #df003a; }
.socialLinks ul li a .fa { font-size: 17px; color: #fff; line-height: 32px; }
.socialLinks ul li.facebook a { background: #2e436f; }
.socialLinks ul li.twitter a { background: #0e84b3; }
.socialLinks ul li.instagram a { background: #047bb4; }
.socialLinks ul li.youtube a { background: #bf2f2a; }
.socialLinks ul li a{padding-left: 4px};
.copyright p { margin-bottom: 0px; font-size: 14px; font-weight: 300; line-height: 1.625em; color: #fff; font-family: 'Roboto', sans-serif; }
/******Content*****/

/*template page css*/
.template .content input, .template .uneditable-input { height: auto; }
.template .content .navbar .container { width: auto; }
.template .content .navbar .brand {     padding: 10px 20px 10px;
    margin-left: -20px;}
