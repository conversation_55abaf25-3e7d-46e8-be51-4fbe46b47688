ALTER PROC dbo.createOrganization
@orgcode varchar(10),
@orgName varchar(100),
@orgshortname varchar(20),
@identityAddress varchar(100),
@identityAddress2 varchar(100),
@identityCity varchar(75),
@identityStateID int,
@identityPostalCode varchar(25),
@identityPhone varchar(40),
@identityFax varchar(40),
@identityEmail varchar(255),
@identityWebsite varchar(400),
@XUserName varchar(100),
@orgID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @groupID int, @orgIdentityID int, @sysMemberID int, @sysMemberNumber varchar(50), @memberTypeID int, @newMemberNumber varchar(50), @recordTypeID int;
	SET @orgID = null;
	SET @orgcode = UPPER(@orgcode);

	IF dbo.fn_isValidUsageCode(@orgcode,'org') = 0
		RAISERROR('Invalid OrgCode.',16,1);

	SELECT @memberTypeID = memberTypeID
	FROM dbo.ams_memberTypes
	WHERE memberType = 'User';

	SET @sysMemberNumber = @orgcode + '_SYSTEM';

	BEGIN TRAN;
		-- add org with default values
		INSERT INTO dbo.organizations (orgcode, hasPrefix, hasMiddleName, hasSuffix, 
			hasProfessionalSuffix, cache_perms_status, usePrefixList, useBatches, memNumPrefixGuest, 
			memNumPrefixUser, accountingEmail, memberNumberOptionID, fiscalYearStartMonth, profLicenseStatusLabel,
			profLicenseNumberLabel, profLicenseDateLabel, invoiceNumPrefix)
		VALUES (@orgcode, 1, 1, 1, 1, 'enabled', 0, 0, @orgcode, @orgcode, '', 1, 1, 'Status', 'License Number', 'Active Date', @orgcode);
		
		SELECT @orgID = SCOPE_IDENTITY();

		EXEC dbo.ams_createOrgIdentity @orgID=@orgID, @organizationName=@orgName, @organizationShortName=@orgshortname, @address1=@identityAddress, @address2=@identityAddress2,
			@city=@identityCity, @stateID=@identityStateID, @postalCode=@identityPostalCode, @phone=@identityPhone, @fax=@identityFax,
			@email=@identityEmail, @website=@identityWebsite, @XUserName=@XUserName, @orgIdentityID=@orgIdentityID OUTPUT;

		-- create default GL Accounts
		EXEC dbo.tr_createDefaultGLAccounts @orgID=@orgID, @orgIdentityID=@orgIdentityID;

		-- create default groups
		EXEC dbo.ams_createDefaultGroups @orgID=@orgID;
		
		-- create default address types/tags
		EXEC dbo.ams_createDefaultMemberAddressTypes @orgID=@orgid;
		
		INSERT INTO dbo.ams_memberAddressTagTypes (orgID, addressTagType, addressTagTypeOrder, isSystemType, allowMembersToUpdate)
		VALUES (@orgid, 'Billing', 1, 1, 1);
		
		-- create default phone types
		EXEC dbo.ams_createDefaultMemberPhoneTypes @orgID=@orgid;

		-- create default email types/tags
		EXEC dbo.ams_createDefaultMemberEmailTypes @orgID=@orgid;

		INSERT INTO dbo.ams_memberEmailTagTypes (orgID, emailTagType, emailTagTypeOrder, isSystemType, allowMembersToUpdate, warnWhenEmpty)
		VALUES (@orgid, 'Primary', 1, 1, 1, 1);

		-- create default website types
		EXEC dbo.ams_createDefaultMemberWebsiteTypes @orgID=@orgid;

		-- create default prof license statuses
		EXEC dbo.ams_createDefaultMemberLicenseStatuses @orgID=@orgid;

		-- add as credit sponsor
		INSERT INTO dbo.crd_sponsors (orgID, statementAppProvider, statementAppProgram, statementPendProgram)
		VALUES (@orgID, '', '', '');

		-- create default record type. This will also create default memberdata view (ams_createVWMemberData)
		EXEC dbo.ams_createRecordType @orgID=@orgID, @recordTypeCode='Individual', @recordTypeName='Individual', @recordTypeDesc='Individual', 
			@isPerson=1, @isOrganization=0, @isDefault=1, @recordTypeID=@recordTypeID OUTPUT;

		-- create org system member account
		EXEC dbo.ams_createMember @orgID=@orgID, @memberTypeID=@memberTypeID, @recordTypeID=@recordTypeID, @prefix=NULL, 
			@firstname='System', @middlename=NULL, @lastname='Account', @suffix=NULL, 
			@professionalsuffix=NULL, @company=NULL, @memberNumber=@sysMemberNumber, @status='A', 
			@bypassQueue=0, @bypassHook=0, @memberID=@sysMemberID OUTPUT, @newMemberNumber=@newMemberNumber OUTPUT

		-- mark member as protected
		UPDATE dbo.ams_members
		SET isProtected = 1
		WHERE memberID = @sysMemberID;

		-- update org sysmemberID
		UPDATE dbo.organizations
		SET sysMemberID = @sysMemberID,
			defaultOrgIdentityID = @orgIdentityID
		WHERE orgID = @orgID;

		-- create sequence
		DECLARE @sql VARCHAR(max);
		SELECT @sql = 'CREATE SEQUENCE dbo.tr_invoiceNum_' + @orgcode + ' AS int START WITH 1 INCREMENT BY 1 CACHE 20;'
		EXEC(@SQL);
	COMMIT TRAN;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
