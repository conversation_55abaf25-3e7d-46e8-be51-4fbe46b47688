<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
<cfoutput>
<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
		<link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
		<link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
		<link rel="stylesheet" href="/css/main.css" />
		#application.objCMS.getBootstrapHeadHTML()#
		#application.objCMS.getResponsiveHeadHTML()#
		<link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
		<link href="/css/responsive.css" rel="stylesheet" type="text/css">
		<link href="https://fonts.googleapis.com/css?family=Poppins:100,100i,200,200i,300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i&display=swap" rel="stylesheet">
		<link href="https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i&display=swap" rel="stylesheet">
		#application.objCMS.getFontAwesomeHTML(includeVersion4Support=true)#
	</head>
	<body>
		<div class="wrapper">
			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
				<header id="header" class=" header outer-width">
					<div class="logoBox">
						<div class="container">
							<a class="brand" href="/">
								<img class="xsHidden" src="/images/logo.png" alt="logo">
								<img class="xsVisible" src="/images/logoMob.png" alt="logo">
							</a>
						</div>
					</div>
					<div id="navbar-example" class="navbar">
						<div class="navbar-inner">
							<div class="navIcon">
							<span class="menu">Menu</span>
							<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
								<span class="icon-bar"></span>
								<span class="icon-bar"></span>
								<span class="icon-bar"></span>
							</a>
							</div>
							<div class="container">
								<div class="navMain hide" id="primaryNavigation">
									<div class="nav-collapse collapse clearfix">
										<cfif structKeyExists(local.strMenus, "primaryNav")>
											#local.strMenus.primaryNav.menuHTML.rawcontent#
										</cfif>
									</div>
								</div>
							</div>
						</div>
					</div>
				</header>
			</cfif>
			<div class="content" <cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">style="padding:20px"<cfelse>style="margin-bottom:30px"</cfif>>
				<div <cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">class="container"</cfif>>
					<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
						<div id="links" style="text-align:right;padding-right:25px">
							<b>
							<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
								<a href="/?pg=login">Login to Education Portal</a>
							<cfelse>
								<a href="/?logout">Logout</a>
							</cfif>
							</b>
						</div>
					</cfif>
					#application.objCMS.renderZone(zone='Main',event=event)#
				</div>
			</div>

			<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
				<footer class="footer"> 
					<div class="footer-top">
						<div class="container">
							<div class="span3 footCol footCol1">
								<h3 class="footerHead">CONTACT US</h3>
								<p>Email: <a href="mailto:<EMAIL>"><EMAIL></a><br>
									Phone: <a href="tel:************">************</a><br>
									Mailing: Alliance for Pharmacy Compounding<br>
									100 Daingerfield Road, Suite 401<br>
									Alexandria, VA 22314
								</p>
							</div>
							<div class="span6 footCol footCol2">
								<h3 class="footerHead">QUICK LINKS</h3>
								<div class="quickLinks">
									<cfif structKeyExists(local.strMenus, "footerNav")>
										#local.strMenus.footerNav.menuHTML.rawcontent#
									</cfif>
								</div>
							</div>
							<div class="span3 footCol footCol3">
								<h3 class="footerHead">Follow Us</h3>
								<div class="socialLinks">
									<cfif application.objCMS.getZoneItemCount(zone='Z',event=event)>
										#application.objCMS.renderZone(zone='Z',event=event)#
									</cfif>
								</div>
							</div>
						</div>
					</div>
					<div class="copyright">
						<div class="container">
							<p>&copy;Alliance for Pharmacy Compounding</p>
						</div>
					</div>
					<cfif application.objCMS.getZoneItemCount(zone='ToolBar',event=event)>
						<div class="noprint">
							#application.objCMS.renderZone(zone='ToolBar',event=event)#
						</div>
					</cfif>
				</footer>
			</cfif>
		</div>
		<script src="/javascript/custom.js"></script>
	</body>
</html>
</cfoutput>