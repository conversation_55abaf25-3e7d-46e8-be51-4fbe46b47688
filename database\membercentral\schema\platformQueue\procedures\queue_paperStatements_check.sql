ALTER PROC dbo.queue_paperStatements_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse datetime, @queueTypeID INT, @grabProcessingStatusID INT, @readyStatusID INT, 
		@grabNotifyStatusID INT, @readyNotifyStatusID INT, @processingStatusID INT, @itemAsStr VARCHAR(60), @xmlMessage xml, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	EXEC dbo.queue_getQueueTypeID @queueType='paperStatements', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@readyNotifyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForNotifying', @queueStatusID=@grabNotifyStatusID OUTPUT;

	-- paperStatements / grabbedForProcessing autoreset to readyToProcess
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyStatusID, 
			dateUpdated = getdate(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @grabProcessingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Paper Statements Queue', @engine='BERLinux';

		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- paperStatements / grabbedForNotifying autoreset to readyToNotify
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @grabNotifyStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyNotifyStatusID, 
			dateUpdated = getdate(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @grabNotifyStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Paper Statements Queue', @engine='BERLinux';

		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue moved items from grabbedForNotifying to readyToNotify';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- paperStatements / processingItem with dateupdated older than 10 minutes
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(minute, -10, GETDATE());
	SELECT @issueCount = count(itemUID) FROM dbo.tblQueueItems WHERE queueStatusID = @processingStatusID AND dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		UPDATE dbo.tblQueueItems
		SET queueStatusID = @readyStatusID, 
			dateUpdated = getdate(),
			jobUID = null,
			jobDateStarted = null
		WHERE queueStatusID = @processingStatusID 
		AND dateUpdated < @timeToUse;

		EXEC membercentral.dbo.sched_resumeTask @name='Paper Statements Queue', @engine='BERLinux';

		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue has items in processingItem with dateupdated older than 10 minutes.';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- paperStatements catchall
	SET @issueCount = 0;
	SET @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(qi.itemUID)
		FROM dbo.tblQueueItems AS qi 
		INNER JOIN dbo.tblQueueStatuses AS qis ON qis.queueStatusID = qi.queueStatusID
		INNER JOIN dbo.tblQueueTypes AS qt ON qt.queueTypeID = qis.queueTypeID
		WHERE qt.queueTypeID = @queueTypeID
		AND (
			(qi.dateUpdated < @timeToUse AND qi.jobUID is null)
			OR
			qi.JobDateStarted < @timeToUse
		);
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'paperStatements Queue Issue';
		SET @errorSubject = 'paperStatements queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
