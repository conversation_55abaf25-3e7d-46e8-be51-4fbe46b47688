<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfset local.selectedTab = arguments.event.getTrimValue("tab","task")>
<cfif arguments.event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>

<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<cfif arguments.event.getValue('projectID') gt 0>
		<script type="text/javascript" src="/assets/admin/javascript/tasks.js#local.assetCachingKey#"></script>
	</cfif>
	<script language="javascript">
		<cfif arguments.event.getValue('projectID') gt 0>
			var details_classificationsTable, prospectcontact_classificationsTable;
			var #ToScript(arguments.event.getValue('mc_siteInfo.siteid'),'mc_siteid')#
			var #ToScript(local.projectID,'project_id')#
			var #ToScript(arguments.event.getValue('workspaceID'),'workspace_id')#
			var #ToScript(local.qryProject.taskFieldLabel,'mc_taskfieldlabel')#
			var #ToScript(local.qryProject.taskFieldLabelPlural,'mc_taskfieldlabelplural')#
			var #ToScript(local.editMemberLink,'link_editmember')#
			var #ToScript(local.memSelectLink,'link_selectmember')#
			var #ToScript(local.grpSelectLink,'link_selectgroup')#
			var #ToScript(arguments.event.getValue('mc_pageDefinition.pageLanguageID'),'link_langid')#
			var #ToScript(local.classificationsListLink,'link_listclassifications')#

			var gridInitArray = new Array();
			gridInitArray["taskTab"] = false;
			gridInitArray["setupTab"] = false;
			gridInitArray["objectivesTab"] = false;
			gridInitArray["additionalDataTab"] = false;
			gridInitArray["automationsTab"] = false;
		
			function onTabChangeHandler(ActiveTab) {
				if (!gridInitArray[ActiveTab.id]) {
					gridInitArray[ActiveTab.id] = true;
					switch(ActiveTab.id) {
						case "taskTab":
							initTasksTable(); break;
						case "solicitorTab":
							initSolicitorsTable(); break;
						case "setupTab":
							initClassificationsList('details'); break;
						case "objectivesTab":
							mccf_initFieldsTable('#local.arrGridData[1].gridExt#');
							<cfloop array="#local.taskTagGridData#" index="local.thisGrid">
								<cfif len(local.thisGrid.gridExt)>
									mccf_initFieldsTable('#local.thisGrid.gridExt#');
								</cfif>
							</cfloop>
							break;
						case "additionalDataTab":
							initClassificationsList('prospectcontact');
							mcgl_initGLAccountsTable('#local.strGivingHistGLAcctWidgetData.gridext#');
							mcgl_initGLAccountsTable('#local.strPaymentSummaryGLAcctWidgetData.gridext#');
							mcev_initEventsTable('#local.strEventWidgetData.gridext#');
							<cfloop list="#local.mhTypeIDList#" index="local.thisMHTypeID">
								mcmh_initMemHistoryTable('#local.qryProject.siteResourceID#_5_#local.thisMHTypeID#');
							</cfloop>
							break;
						case "automationsTab":
							getTaskAutomations(); break;
					}
				}
			}
			function removeClassification(cID,area) {
				var removeClassData = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						reloadClassificationTable(area);
					} else {
						delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						alert('We were unable to delete this group set. Try again.');
					}
				};

				let delBtn = $('##btnDelClass'+ '_'+area+cID);
				mca_initConfirmButton(delBtn, function(){
					var objParams = { classificationID:cID };
					TS_AJX('ADMMEMBERSETTINGS','deleteClassification',objParams,removeClassData,removeClassData,10000,removeClassData);
				});
			}
			function moveClassification(cid,list,area,dir) {
				var moveResult	= function(r) {
					if (r.success && r.success.toLowerCase() == 'true')
						mca_moveDataTableRow('prospectClassification_'+cid,dir,'moveGrpSetUp'+area,'moveGrpSetDown'+area);
					else alert('We were unable to move this group set row. Try again.');
				};
				var objParams = { classificationID:cid, dir:dir };
				TS_AJX('ADMMEMBERSETTINGS','doClassificationMove',objParams,moveResult,moveResult,10000,moveResult);
			}
			function editClassification(cID,srID,area){
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: cID > 0 ? 'Edit Classification' : 'Add Classification',
					iframe: true,
					contenturl: '#local.editClassificationLink#&classificationID=' + cID + '&siteResourceID=' + srID + '&area=' + area,
					strmodalfooter : {
						classlist: 'd-flex',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary ml-auto',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSaveClassification").click',
						extrabuttonlabel: 'Save Details',
					}
				});
			}
		</cfif>

		function closeBox() { MCModalUtils.hideModal(); }

		$(function() {
			<cfif arguments.event.getValue('projectID') gt 0>
				mca_initNavPills('projectTabs', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
			<cfelse>
				mca_initNavPills('projectTabs');
			</cfif>
			mca_setupSelect2();
		});
	</script>
	<style type="text/css">
		div.bn { padding:2px; }
		div.bn2 { padding-top: 2px; }
		div.nmsg { padding:4px 10px 0 10px; } 
		.medim { color: ##808080; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<cfif arguments.event.getValue('projectID') gt 0>
	<h4>View Project - #local.qryProject.projectName# (#local.qryProject.applicationInstanceName#)</h4>
<cfelse>
	<h4>Adding Project to #arguments.event.getValue('appInstanceName')#</h4>
</cfif>

<div id="divTasksInfoContainer" class="d-none"></div>

<ul id="projectTabs" class="nav nav-pills nav-pills-dotted">
	<cfif arguments.event.getValue('projectID') gt 0>
		<cfset local.thisTabName = "task">
		<cfset local.thisTabID = "taskTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">#local.qryProject.taskFieldLabelPlural#</a>
		</li>

		<cfset local.thisTabName = "solicitor">
		<cfset local.thisTabID = "solicitorTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">#local.qryProject.solicitorFieldLabelPlural#</a>
		</li>
	</cfif>

	<cfset local.thisTabName = "setup">
	<cfset local.thisTabID = "setupTab">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Project Setup</a>
	</li>

	<cfif arguments.event.getValue('projectID') gt 0>
		<cfset local.thisTabName = "objectives">
		<cfset local.thisTabID = "objectivesTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Objectives</a>
		</li>

		<cfset local.thisTabName = "additionalData">
		<cfset local.thisTabID = "additionalDataTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">#local.qryProject.prospectFieldLabel# Data</a>
		</li>

		<cfset local.thisTabName = "automations">
		<cfset local.thisTabID = "automationsTab">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Automations</a>
		</li>
	</cfif>
</ul>

<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
	<cfif arguments.event.getValue('projectID') gt 0>
		<div id="pills-taskTab" class="tab-pane fade" role="tabpanel" aria-labelledby="task">
			<cfinclude template="frm_project_tasks.cfm">
		</div>
		<div id="pills-solicitorTab" class="tab-pane fade" role="tabpanel" aria-labelledby="solicitor">
			<cfinclude template="frm_project_solicitors.cfm">
		</div>
	</cfif>
	<div id="pills-setupTab" class="tab-pane fade" role="tabpanel" aria-labelledby="setup">
		<cfinclude template="frm_project_general.cfm">
	</div>
	<cfif arguments.event.getValue('projectID') gt 0>
		<div id="pills-objectivesTab" class="tab-pane fade" role="tabpanel" aria-labelledby="objectives">
			<cfinclude template="frm_project_custom.cfm">
		</div>
		<div id="pills-additionalDataTab" class="tab-pane fade" role="tabpanel" aria-labelledby="additionalData">
			<cfinclude template="frm_project_additionalData.cfm">
		</div>
		<div id="pills-automationsTab" class="tab-pane fade" role="tabpanel" aria-labelledby="automations">
			<cfinclude template="frm_project_automations.cfm">
		</div>
	</cfif>
</div>
</cfoutput>