<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct">
	<cfoutput>
	<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
	<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
		<link rel="stylesheet" type="text/css" href="/css/main.css" media="screen"/> 
	</head>
	<body class="direct">
		<div class="bodyText" style="margin:20px;">#application.objCMS.renderZone(zone='Main',event=event)#</div>
	</body>
	</html>
	</cfoutput>
<cfelse>
	<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
	<cfoutput>
		<!doctype html>
		<html>
			<head>
			<meta charset="UTF-8">
			<meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
			<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
			<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
			
			#application.objCMS.getBootstrapHeadHTML()#
			#application.objCMS.getResponsiveHeadHTML()#
			<link href="https://ce.orthodontics.com/css/main.css" rel="stylesheet" type="text/css">
			<link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
			<link href="/css/responsive.css" rel="stylesheet" type="text/css">
			<link href="https://fonts.googleapis.com/css?family=PT+Sans:400,700" rel="stylesheet">
			<link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
			<script src="/javascript/custom.js" type="text/javascript"></script>
			</head>			
			<body>
				<div id="wrapper" class="wrapper outer-width in-wrapper"> 
				  <!--Header Start-->
					<header id="header" class=" header outer-width animated fadeIn wow">
						<div id="navbar-example" class="navbar navbar-static">
							<div class="navbar-inner">
								<div class="container">
									<div class="navIn"> <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"> <span class="icon-bar"></span> 	<span class="icon-bar"></span> <span class="icon-bar"></span> </a>
										<div class="header-in row-fluid">
											<div class="span4 header-top-cols"> <a class="brand" href="/"><img src="/images/logo.png" alt="logo"></a> </div>
											<div class="span5 header-top-cols">
												<p>Kansas Funeral Directors Association</p>
											</div>
											<div class="span3 header-top-cols"> <img src="/images/Building drawing.jpg" alt="image"> </div>
										</div>
										<div class="navMain">
											<div class="nav-collapse collapse clearfix">
												<cfif structKeyExists(local.strMenus, "primaryNav")>
													#local.strMenus.primaryNav.menuHTML.rawcontent#
												</cfif>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
							<!-- /navbar- --> 
					</header>
					<!--Header End--> 
					<!--COntent Start-->
					<div class="content">
						<div class="container">
							<div>
								<div class="container-fluid pull-right">
									<div class="row-fluid"> 
										<strong>
											<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
												<a href="/?pg=login">Login to Education Portal</a>
											<cfelse>
												<a href="/?logout">Logout</a>
											</cfif>
										</strong>
									</div>
								</div>
							</div>	
						</div>
						<div class="container">
							#application.objCMS.renderZone(zone='Main',event=event)#
						</div>	
					</div>					
					<!--Content End--> 
					<!--Footer Start-->
					<footer class="footer">
						<div class="container">
							<p>Copyright @ 2015 Kansas Funeral Directors Association.  All Rights Reserved.</p>
						</div>
					</footer>
				</div>
				<cfif application.objCMS.getZoneItemCount(zone='ToolBar',event=event)>
					<div id="toolBarArea">
						<div id="toolBar">#application.objCMS.renderZone(zone='ToolBar',event=event)#</div>
					</div>
				</cfif>
						
			</body>
		</html>
	</cfoutput>
</cfif>
