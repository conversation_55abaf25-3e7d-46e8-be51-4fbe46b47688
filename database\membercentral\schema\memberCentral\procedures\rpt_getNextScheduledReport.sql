ALTER PROC dbo.rpt_getNextScheduledReport

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @nowDate datetime = getdate(), @itemID int;
	DECLARE @tblScheduledReport table (itemID int PRIMARY KEY);

	-- dequeue the next report to run in order of nextRunDate
	UPDATE r WITH (UPDLOCK, READPAST)
	SET r.isRunning = 1,
		r.dateLastUpdated = @nowDate
		OUTPUT inserted.itemID
		INTO @tblScheduledReport (itemID)
	FROM dbo.rpt_scheduledReports as r
	INNER JOIN (
		SELECT TOP 1 itemID
		FROM dbo.rpt_scheduledReports
		WHERE isRunning = 0
		AND nextRunDate < @nowDate
		ORDER BY nextRunDate
	) as batch on batch.itemID = r.itemID
	WHERE r.isRunning = 0;

	SELECT @itemID = itemID FROM @tblScheduledReport;
	SET @itemID = ISNULL(@itemID,0);

	-- return the report info
	SELECT @itemID as itemID, s.siteCode, tt.toolType, tt.toolDesc, tt.toolCFC, r.reportAction, 
		rpt.reportID, rpt.reportName, rpt.[uid] as reportUID, r.fileName, r.toEmail, r.emailbody
	FROM dbo.rpt_scheduledReports as r
	INNER JOIN dbo.sites as s on s.siteID = r.siteID
	INNER JOIN dbo.rpt_SavedReports AS rpt ON rpt.reportID = r.reportID
	INNER JOIN dbo.admin_toolTypes AS tt ON tt.toolTypeID = rpt.toolTypeID
	WHERE r.itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
