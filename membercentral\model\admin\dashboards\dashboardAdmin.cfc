<cfcomponent extends="model.admin.admin" output="no">
	<cfset this.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// set rights into event
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="listDashboards" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objDashboard = createObject("component","dashboard");
			local.tmpRights = arguments.event.getValue('mc_admintoolInfo.myRights');
			local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);

			local.qryDashboards = local.objDashboard.getDashboards(siteID=arguments.event.getValue('mc_siteInfo.siteid'), mode="list");
			local.qryMemberJoinDateFields = local.objDashboard.getMemberJoinDateFields(siteCode=arguments.event.getValue('mc_siteInfo.siteCode'));
			local.arrMemberJoinDateFields = getArrayOfMemberJoinDateFields(qryMemberJoinDateFields=local.qryMemberJoinDateFields);
			local.qryLists = getLists(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.viewDashboardLink = buildCurrentLink(arguments.event,"viewDashboard");
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_dashboards.cfm">
		</cfsavecontent>

		<!--- log view --->
		<cfset logDashboardView(siteID=arguments.event.getValue('mc_siteInfo.siteID'), dashboardID=0, memberID=session.cfcuser.memberdata.memberID)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editDashboardObject" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.tmpRights = arguments.event.getValue('mc_admintoolInfo.myRights')>
		<cfset local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID)>

		<cfset local.objDashboard = createObject("component","dashboard")>
		<cfset local.dashboardID = int(val(arguments.event.getValue('dashboardID',0)))>

		<cfset local.qryDashboard = local.objDashboard.getDashboard(siteID=arguments.event.getValue('mc_siteInfo.siteid'), dashboardID=local.dashboardID)>
		
		<!--- security --->
		<cfif NOT (local.tmpRights.editAllDashboards OR (local.tmpRights.editOwnDashboards AND local.qryDashboard.memberID eq session.cfcuser.memberdata.memberID))>
			<cfreturn returnAppStruct("You do not have rights to this section.","echo")>
		</cfif>

		<cfset local.qryDashboardObjectTypes = local.objDashboard.getDashboardObjectTypes(siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.qryDashboardObject = local.objDashboard.getDashboardObject(siteID=arguments.event.getValue('mc_siteInfo.siteid'), orgID=arguments.event.getValue('mc_siteInfo.orgID'), 
											dashboardID=local.dashboardID, objectID=arguments.event.getValue('objID',0))>
		<cfset local.qryDashboardCategories = local.objDashboard.getDashboardCategories(siteID=arguments.event.getValue('mc_siteInfo.siteid'), dashboardID=local.dashboardID)>
		<cfset local.qryMemberJoinDateFields = local.objDashboard.getMemberJoinDateFields(siteCode=arguments.event.getValue('mc_siteInfo.siteCode'))>
		<cfset local.qryDashboardObjectVisualTypes = local.objDashboard.getDashboardObjectVisualTypes()>
		<cfset local.isEditMode = local.qryDashboardObject.recordCount eq 1>
		
		<cfquery name="local.qryObjSections" dbtype="query">
			select distinct section
			from [local].qryDashboardObjectTypes
			order by section
		</cfquery>

		<cfquery name="local.arrObjTypes" dbtype="query" returntype="array">
			select objectTypeID, section, objectTypeCode, objectTypeTitle, objectTypeDesc, objectTypeTechDesc, 
				useRevenueGLFilter, useInvoiceProfileFilter, useSubscriptionFilter, useMemberDatesConfig, 
				useListFilter, usePanelFilter, useGroupsetFilter, useCalendarFilter, useNumericMemberCustomFieldFilter,
				objectWidth, objectHeight
			from [local].qryDashboardObjectTypes
			<cfif not local.qryMemberJoinDateFields.recordCount>
				where useMemberDatesConfig = 0
			</cfif>
			order by section, objectTypeTitle
		</cfquery>

		<cfset local.arrMemberJoinDateFields = arrayNew(1)>
		<cfif local.qryMemberJoinDateFields.recordCount>
			<cfset local.arrMemberJoinDateFields = getArrayOfMemberJoinDateFields(qryMemberJoinDateFields=local.qryMemberJoinDateFields)>
			<cfif local.isEditMode and len(local.qryDashboardObject.objectDataXML)>
				<cfset local.udid = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/udid/text())")>
			<cfelse>
				<cfset local.udid = 0>
			</cfif>
		</cfif>

		<cfif arguments.event.getValue('mc_siteInfo.sf_subscriptions')>
			<cfset local.qrySubscriptionTypes = CreateObject("component","model.admin.subscriptions.subscriptions").getSubscriptionTypes(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
			<cfif local.isEditMode and len(local.qryDashboardObject.objectDataXML)>
				<cfset local.typeIDList = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/subtype/text())")>
				<cfset local.subIDList = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/subid/text())")>
			<cfelse>
				<cfset local.typeIDList = "">
				<cfset local.subIDList = "">
			</cfif>
		</cfif>

		<cfset local.strGLAccountWidgetData = { title='', description='', gridext="#this.siteResourceID#_1", gridwidth=670, gridheight=150, initGridOnLoad=true, 
												controllingSRID=this.siteResourceID, reportID=val(local.qryDashboardObject.objectID), glatid=3, 
												extraNodeName='gl', widgetMode='dashboard' }>
		<cfset local.strGLAccountWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strGLAccountWidgetData)>

		<cfset local.qryLists = getLists(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfif local.isEditMode and len(local.qryDashboardObject.objectDataXML)>
			<cfset local.listNameList = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/list/text())")>
		<cfelse>
			<cfset local.listNameList = "">
		</cfif>

		<cfif arguments.event.getValue('mc_siteInfo.hasReferrals')>
			<cfset local.strReferralPanelWidgetData = { title='Optionally, limit this chart to the following referral panels:', titleTag='h6', gridext="#this.siteResourceID#_1", initGridOnLoad=false, 
						controllingSRID=this.siteResourceID, itemID=val(local.qryDashboardObject.objectID), extraNodeName='panel', widgetMode='dashboard' }>
			<cfset local.strReferralPanelWidget = createObject("component","model.admin.common.modules.referralPanelWidget.referralPanelWidget").renderWidget(strWidgetData=local.strReferralPanelWidgetData)>
		</cfif>

		<cfset local.qryInvoiceProfiles = createObject("component","model.admin.transactions.invoice").getInvoiceProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfif local.isEditMode and local.qryDashboardObject.useInvoiceProfileFilter eq 1 and len(local.qryDashboardObject.objectDataXML)>
			<cfset local.invoiceProfileIDList = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/ip/text())")>
		<cfelse>
			<cfset local.invoiceProfileIDList = "">
		</cfif>

		<cfset local.qryGroupSets = createObject("component","model.admin.MemberGroupSets.MemberGroupSets").getGroupSets(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfif local.isEditMode and local.qryDashboardObject.useGroupsetFilter eq 1 and len(local.qryDashboardObject.objectDataXML)>
			<cfset local.groupSetID = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/gsid/text())")>
		<cfelse>
			<cfset local.groupSetID = "">
		</cfif>

		<cfset local.qryNumericCustomFields = local.objDashboard.getNumericCustomFields(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfif local.isEditMode and local.qryDashboardObject.useNumericMemberCustomFieldFilter eq 1 and len(local.qryDashboardObject.objectDataXML)>
			<cfset local.customFieldColumnID = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/cid/text())")>
		<cfelse>
			<cfset local.customFieldColumnID = "">
		</cfif>

		<!--- Events --->
		<cfset local.arrNodes = XMLSearch(application.adminNavigationXML,"/navitems/navitem[@navName='Events']/navitem/navitem[@navName='Events']")>
		<cfif structKeyExists(session.mcastruct.strNavKeys, local.arrNodes[1].xmlAttributes.navKey)>
			<cfset local.showEvents = true>
		<cfelse>
			<cfset local.showEvents = false>
		</cfif>

		<cfif local.showEvents>
			<cfset local.qryCalendars = CreateObject("component","model.admin.events.event").getCalendarsForFilters(arguments.event.getValue('mc_siteInfo.siteID'))>
			<cfif local.isEditMode and len(local.qryDashboardObject.objectDataXML)>
				<cfset local.evCalendarIDList = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/evcal/text())")>
				<cfset local.evCategoryIDList = XMLSearch(local.qryDashboardObject.objectDataXML,"string(/obj/evcat/text())")>
			<cfelse>
				<cfset local.evCalendarIDList = "">
				<cfset local.evCategoryIDList = "">
			</cfif>
		</cfif>

		<cfset local.strVisualTypes = {}>
		<cfoutput query="local.qryDashboardObjectVisualTypes" group="objectTypeID">
			<cfset local.arrVisualTypes = []>
			<cfoutput>
				<cfset arrayAppend(local.arrVisualTypes,{
					"objectTypeCode": local.qryDashboardObjectVisualTypes.objectTypeCode,
					"visualTypeID": local.qryDashboardObjectVisualTypes.visualTypeID,
					"visualTypeCode": local.qryDashboardObjectVisualTypes.visualTypeCode,
					"visualType": local.qryDashboardObjectVisualTypes.visualType,
					"defaultvisualtypeid": local.qryDashboardObjectVisualTypes.defaultVisualTypeID
				})>
			</cfoutput>
			<cfset local.strVisualTypes[local.qryDashboardObjectVisualTypes.objectTypeID] = local.arrVisualTypes>
		</cfoutput>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_dashboardObject.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewDashboard" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objDashboard = CreateObject("component","dashboard")>
		<cfset local.tmpRights = arguments.event.getValue('mc_admintoolInfo.myRights')>
		<cfset local.listDashboardsLink = buildCurrentLink(arguments.event,"listDashboards")>
		<cfset local.siteID = arguments.event.getValue('mc_siteinfo.siteID')>
		
		<cfif NOT validateParameters(event=arguments.event, mode="viewDashboard")>
			<cflocation url="#local.listDashboardsLink#" addtoken="false">
		</cfif>

		<cfset local.dashboardID = arguments.event.getValue('dashboardID')>
		<cfset local.qryDashboard = local.objDashboard.getDashboard(siteID=local.siteID, dashboardID=local.dashboardID)>

		<cfif NOT local.qryDashboard.recordCount OR 
				(NOT (local.tmpRights.viewAllDashboards OR (local.tmpRights.viewOwnDashboards AND local.qryDashboard.memberID eq session.cfcuser.memberdata.memberID)))>
			<cflocation url="#local.listDashboardsLink#" addtoken="false">
		</cfif>

		<cfset local.editDashboardMode = arguments.event.getValue('editMode')>
		<cfset local.hasEditRights = local.tmpRights.editAllDashboards OR (local.tmpRights.editOwnDashboards AND local.qryDashboard.memberID eq session.cfcuser.memberdata.memberID)>
		<cfset local.hasDeleteRights = local.tmpRights.deleteAllDashboards OR (local.tmpRights.deleteOwnDashboards AND local.qryDashboard.memberID eq session.cfcuser.memberdata.memberID)>

		<cfset local.qryDashboards = local.objDashboard.getDashboards(siteID=local.siteID, mode="view")>
		<cfset local.qryDashboardObjects = local.objDashboard.getDashboardObjects(siteID=local.siteID, dashboardID=local.dashboardID)>
		
		<cfset local.viewDashboardLink = buildCurrentLink(arguments.event,"viewDashboard")>
		<cfset local.editDashboardObjectLink = buildCurrentLink(arguments.event,"editDashboardObject") & '&dashboardID=#local.qryDashboard.dashboardID#&mode=stream'>

		<cfquery name="local.qryDashboardObjectsToRender" dbtype="query">
			SELECT objectID
			FROM [local].qryDashboardObjects
			WHERE objectID > 0
		</cfquery>

		<cfset appendBreadCrumbs(arguments.event,{ link='', text=local.qryDashboard.dashboardName })>

		<cfset local.showChartLastUpdated = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_dashboard_view.cfm">
		</cfsavecontent>

		<cfif local.qryDashboard.updatesPaused and NOT local.qryDashboard.maskData>
			<cfset local.objDashboard.resumeDashboardUpdates(siteID=local.siteID, dashboardID=local.qryDashboard.dashboardID)>
		</cfif>

		<cfset logDashboardView(siteID=local.siteID, dashboardID=local.qryDashboard.dashboardID, memberID=session.cfcuser.memberdata.memberID)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="validateParameters" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">
		<cfargument name="mode" type="string" required="true">

		<cfscript>
		var local = structNew();
		
		// mode specific validations
		switch(arguments.mode){
			case 'viewDashboard':
				arguments.event.paramValue('dashboardID',0);
				arguments.event.paramValue('editMode',0);

				// id needs to be a simple value. URLs like   id..id= will result in id being a struct
				if (NOT isSimpleValue(arguments.event.getValue('dashboardID')))
					return false;
				if (NOT isSimpleValue(arguments.event.getValue('editMode')))
					return false;

				// ID must be valid positive integer
				arguments.event.setValue('dashboardID',int(val(arguments.event.getValue('dashboardID'))));

				if (NOT isValid("integer",arguments.event.getValue('dashboardID')) or arguments.event.getValue('dashboardID') lt 1)
					return false;
				if (NOT isValid("boolean",arguments.event.getValue('editMode')))
					return false;
				break;
		}

		return true;
		</cfscript>
	</cffunction>

	<cffunction name="getArrayOfMemberJoinDateFields" access="private" output="false" returntype="array">
		<cfargument name="qryMemberJoinDateFields" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.arrMemberJoinDateFields = arrayNew(1)>

		<cfif arguments.qryMemberJoinDateFields.recordCount>
			<cfset local.fieldNameList = "joinDateFieldName|Join,rejoinDateFieldName|Rejoin,droppedDateFieldName|Drop,paidThruDateFieldName|Paid Thru,renewalDateFieldName|Renewal">
			<cfloop query="arguments.qryMemberJoinDateFields">
				<cfset local.dateFields = "">
				<cfloop list="#local.fieldNameList#" index="local.thisField">
					<cfset local.thisCol = listFirst(local.thisField,'|')>
					<cfset local.thisFld = listLast(local.thisField,'|')>
					<cfif len(arguments.qryMemberJoinDateFields[local.thisCol][arguments.qryMemberJoinDateFields.currentRow])>
						<cfset local.dateFields = "#local.dateFields##arguments.qryMemberJoinDateFields[local.thisCol][arguments.qryMemberJoinDateFields.currentRow]# (#local.thisFld#), ">
					</cfif>
				</cfloop>
				<cfif len(local.dateFields)>
					<cfset local.tmpStr = { udid = arguments.qryMemberJoinDateFields.udid, configName = left(local.dateFields,len(local.dateFields)-2) }>
					<cfset arrayAppend(local.arrMemberJoinDateFields,local.tmpStr)>
				</cfif>
			</cfloop>
		</cfif>

		<cfreturn local.arrMemberJoinDateFields>
	</cffunction>

	<cffunction name="getLists" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryLists = "">

		<cfquery name="qryLists" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT mcl.listName
			FROM dbo.lists_lists mcL 
			INNER JOIN dbo.cms_siteResources lsr ON lsr.siteID = @siteID and mcl.siteResourceID = lsr.siteResourceID and lsr.siteResourceStatusID = 1
			INNER JOIN LYRIS.trialslyris1.dbo.lists_format tlaLF ON tlaLF.name = mcL.listName collate Latin1_General_CI_AI
			INNER JOIN LYRIS.trialslyris1.dbo.lists_ as tlaL ON tlaL.name_ = tlaLF.Name collate Latin1_General_CI_AI
			ORDER BY mcl.listName;
		</cfquery>

		<cfreturn qryLists>
	</cffunction>

	<cffunction name="logDashboardView" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="dashboardID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryLog = "">

		<cfquery name="qryLog" datasource="#application.dsn.platformStatsMC.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
			DECLARE @dashboardID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.dashboardID#">;
			DECLARE @memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;

			INSERT INTO dbo.rpt_dashboardViews (siteID, dashboardID, memberID)
			VALUES (@siteID, @dashboardID, @memberID);
		</cfquery>
	</cffunction>

</cfcomponent>