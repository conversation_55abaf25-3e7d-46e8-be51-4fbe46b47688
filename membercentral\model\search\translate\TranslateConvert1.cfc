<cfcomponent>

	<!--- From 1-website --->
	<cffunction name="convert_1_1" access="public" output="no" returntype="xml" hint="from 1-Website to 1-Website. put in quotes around name.">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:if test="string-length(s_fname) &gt; 0"><xsl:text>"</xsl:text></xsl:if><xsl:value-of select="s_fname"/><xsl:if test="string-length(s_fname) &gt; 0"><xsl:text>"</xsl:text></xsl:if></xsl:element>
					<xsl:element name="s_lname"><xsl:if test="string-length(s_lname) &gt; 0"><xsl:text>"</xsl:text></xsl:if><xsl:value-of select="s_lname"/><xsl:if test="string-length(s_lname) &gt; 0"><xsl:text>"</xsl:text></xsl:if></xsl:element>
					<xsl:element name="s_moddatefrom"><xsl:value-of select="translate(s_moddatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_moddateto"><xsl:value-of select="translate(s_moddateto,'Z','')"/></xsl:element>
					<xsl:element name="s_siteresourceid"><xsl:attribute name="expanded"><xsl:value-of select="s_siteresourceid/@expanded"/></xsl:attribute><xsl:value-of select="s_siteresourceid"/></xsl:element>
					<xsl:element name="s_applicationinstanceid"><xsl:attribute name="expanded"><xsl:value-of select="s_applicationinstanceid/@expanded"/></xsl:attribute><xsl:value-of select="s_applicationinstanceid"/></xsl:element>
					<xsl:element name="s_type"><xsl:value-of select="substring(s_type,1,1)"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_2" access="public" output="no" returntype="xml" hint="from 1-Website to 2-SeminarWeb">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_cle"/>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_pub"/>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_ft">SWOD,SWTL,SWL,CONF</xsl:element>
					<xsl:element name="s_datefrom"></xsl:element>
					<xsl:element name="s_dateto"></xsl:element>															
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_3" access="public" output="no" returntype="xml" hint="from 1-Website to 3-Lyris">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_postdatefrom"><xsl:value-of select="translate(s_moddatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_postdateto"><xsl:value-of select="translate(s_moddateto,'Z','')"/></xsl:element>
					<xsl:element name="s_list"></xsl:element>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_attachment"></xsl:element>
					<xsl:element name="s_postedby"></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_5" access="public" output="no" returntype="xml" hint="from 1-Website to 5-Depositions">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_casename"></xsl:element>
					<xsl:element name="s_depodatefrom"></xsl:element>
					<xsl:element name="s_depodateto"></xsl:element>
					<xsl:element name="s_jurisdiction"></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_6" access="public" output="no" returntype="xml" hint="from 1-Website to 6-Store">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_category"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/><xsl:text> </xsl:text><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
					<xsl:element name="s_datefrom"/>
					<xsl:element name="s_dateto"/>				
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_8" access="public" output="no" returntype="xml" hint="from 1-Website to 8-Mdex">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_casename"></xsl:element>
					<xsl:element name="s_chaldatefrom"></xsl:element>
					<xsl:element name="s_chaldateto"></xsl:element>
					<xsl:element name="s_jurisdiction"></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_10" access="public" output="no" returntype="xml" hint="from 1-Website to 10-SimSearches">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_datefrom"></xsl:element>
					<xsl:element name="s_dateto"></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_12" access="public" output="no" returntype="xml" hint="from 1-Website to 12-CourtDocs">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_documentname"><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_type"/>
					<xsl:element name="s_cause"/>
					<xsl:element name="s_ownership"/>
					<xsl:element name="s_docflags"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_14" access="public" output="no" returntype="xml" hint="from 1-Website to 14-MyDocuments">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_casename"/>
					<xsl:element name="s_depodatefrom"/>
					<xsl:element name="s_depodateto"/>
					<xsl:element name="s_jurisdiction"/>
					<xsl:element name="s_type"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_15" access="public" output="no" returntype="xml" hint="from 1-Website to 15-disciplinaryActions">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>							
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_16" access="public" output="no" returntype="xml" hint="from 1-Website to 16-WA_TrialNews">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_category"/>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_datefrom"/>
					<xsl:element name="s_dateto"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_18" access="public" output="no" returntype="xml" hint="from 1-Website to 18-FILESHARE">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_applicationinstanceid"/>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_category"/>
					<xsl:element name="s_fname" />
					<xsl:element name="s_lname" />
					<xsl:element name="s_datefrom"/>
					<xsl:element name="s_dateto"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_22" access="public" output="no" returntype="xml" hint="from 1-Website to 22-SDCBA_LEQ">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_code" />
					<xsl:element name="s_topic" />
					<xsl:element name="s_key_all"><xsl:value-of select="s_fname"/><xsl:if test="string-length(s_fname) &gt; 0"><xsl:text> </xsl:text></xsl:if><xsl:value-of select="s_lname"/><xsl:if test="string-length(s_lname) &gt; 0"><xsl:text> </xsl:text></xsl:if><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_23" access="public" output="no" returntype="xml" hint="from 1-Website to 23-MemberDirectory">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_firm"/>
					<xsl:element name="s_city"/>
					<xsl:element name="s_zipradius"/>
					<xsl:element name="s_zip"/>
					<xsl:element name="s_state"/>
					<xsl:element name="s_classification"/>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_24" access="public" output="no" returntype="xml" hint="from 1-Website to 24-Events">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_applicationinstanceid" />
					<xsl:element name="s_datefrom"><xsl:value-of select="translate(s_moddatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_dateto"><xsl:value-of select="translate(s_moddateto,'Z','')"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/><xsl:text> </xsl:text><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
					
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_25" access="public" output="no" returntype="xml" hint="from 1-Website to 25-Blogs">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_applicationinstanceid"/>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_category"/>
					<xsl:element name="s_topic"/>
					<xsl:element name="s_postdatefrom"/>
					<xsl:element name="s_postdateto"/>
					<xsl:element name="s_moddatefrom"><xsl:value-of select="translate(s_moddatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_moddateto"><xsl:value-of select="translate(s_moddateto,'Z','')"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_28" access="public" output="no" returntype="xml" hint="from 1-Website to 28-FL_JDB">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_category"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_30" access="public" output="no" returntype="xml" hint="from 1-Website to 30-ON_LDB">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_category"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_34" access="public" output="no" returntype="xml" hint="from 1-Website to 34-FILESHARE2">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_applicationinstanceid"><xsl:value-of select="s_applicationinstanceid"/></xsl:element>
					<xsl:element name="s_category"/>
					<xsl:element name="s_aut"><xsl:value-of select="s_aut"/></xsl:element>
					<xsl:element name="s_fname"/>
					<xsl:element name="s_lname"/>
					<xsl:element name="s_datefrom"><xsl:value-of select="translate(s_datefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_dateto"><xsl:value-of select="translate(s_dateto,'Z','')"/></xsl:element>
					<xsl:element name="s_depodatefrom"><xsl:value-of select="translate(s_depodatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_depodateto"><xsl:value-of select="translate(s_depodateto,'Z','')"/></xsl:element>
					<xsl:element name="s_type"><xsl:value-of select="substring(s_type,1,1)"/></xsl:element>
					<xsl:element name="s_docflags"/>
					<xsl:element name="s_jurisdiction"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/><xsl:text> </xsl:text><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
					<xsl:element name="s_postdatefrom"><xsl:value-of select="translate(s_postdatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_postdateto"><xsl:value-of select="translate(s_postdateto,'Z','')"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>		
	<cffunction name="convert_1_35" access="public" output="no" returntype="xml" hint="from 1-Website to 35-Introduction">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_36" access="public" output="no" returntype="xml" hint="from 1-Website to 36-ON_ExpertDatabase">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8"/> 
			<xsl:template match="/"><xsl:apply-templates/></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_category"/>
					<xsl:element name="s_firm"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_37" access="public" output="no" returntype="xml" hint="from 1-Website to 37-OH_JDB">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_category"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/><xsl:text> </xsl:text><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_38" access="public" output="no" returntype="xml" hint="from 1-Website to 38-OH_VSDB">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_category"/>
					<xsl:element name="s_fname"/>
					<xsl:element name="s_lname"/>
					<xsl:element name="s_datefrom"><xsl:value-of select="translate(s_moddatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_dateto"><xsl:value-of select="translate(s_moddateto,'Z','')"/></xsl:element>
					<xsl:element name="s_type"/>
					<xsl:element name="s_jurisdiction"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/><xsl:text> </xsl:text><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_41" access="public" output="no" returntype="xml" hint="from 1-Website to 41-GA_VSDB">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_fname"/>
					<xsl:element name="s_lname"/>
					<xsl:element name="s_datefrom"><xsl:value-of select="translate(s_moddatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_dateto"><xsl:value-of select="translate(s_moddateto,'Z','')"/></xsl:element>
					<xsl:element name="s_type"/>
					<xsl:element name="s_jurisdiction"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_42" access="public" output="no" returntype="xml" hint="from 1-Website to 42-NH_VSDB">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_cat"></xsl:element>
					<xsl:element name="s_category"></xsl:element>
					<xsl:element name="s_fname"></xsl:element>
					<xsl:element name="s_lname"></xsl:element>
					<xsl:element name="s_jurisdiction"></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_43" access="public" output="no" returntype="xml" hint="from 1-Website  to 43-SK_AADB">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_category"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>	
	<cffunction name="convert_1_44" access="public" output="no" returntype="xml" hint="from 1-Website to 44-AAJPackets">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_category"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_45" access="public" output="no" returntype="xml" hint="from 1-Website to 45-OH_DOGBITE">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_aut"/>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/><xsl:text> </xsl:text><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_46" access="public" output="no" returntype="xml" hint="from 1-Website to 46-Medline">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_47" access="public" output="no" returntype="xml" hint="from 1-Website to 47-Publications">
		<cfargument name="stSearchXML" required="yes" type="xml">
		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_applicationinstanceid"><xsl:value-of select="s_applicationinstanceid"/></xsl:element>
					<xsl:element name="s_aut"><xsl:value-of select="s_aut"/></xsl:element>
					<xsl:element name="s_category"><xsl:value-of select="s_category"/></xsl:element>
					<xsl:element name="s_topic"><xsl:attribute name="expanded"><xsl:value-of select="s_topic/@expanded"/></xsl:attribute><xsl:value-of select="s_topic"/></xsl:element>
					<xsl:element name="s_postdatefrom"><xsl:value-of select="translate(s_postdatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_postdateto"><xsl:value-of select="translate(s_postdateto,'Z','')"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_48" access="public" output="no" returntype="xml" hint="from 1-Website to 48-daubertStudy">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>
		
		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_49" access="public" output="no" returntype="xml" hint="from 1-Website to 49-LyrisOther">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
					<xsl:element name="s_postdatefrom"><xsl:value-of select="translate(s_moddatefrom,'Z','')"/></xsl:element>
					<xsl:element name="s_postdateto"><xsl:value-of select="translate(s_moddateto,'Z','')"/></xsl:element>
					<xsl:element name="s_cat"/>
					<xsl:element name="s_attachment"></xsl:element>
					<xsl:element name="s_postedby"></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_50" access="public" output="no" returntype="xml" hint="from 1-Website to 50-ExpertConnect">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_fname"><xsl:value-of select="s_fname"/></xsl:element>
					<xsl:element name="s_lname"><xsl:value-of select="s_lname"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>
	<cffunction name="convert_1_51" access="public" output="no" returntype="xml" hint="from 1-Website to 51-Decisis">
		<cfargument name="stSearchXML" required="yes" type="xml">

		<cfset var local = StructNew()>
		<cfsavecontent variable="local.stXSL">
			<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
			<xsl:output method="xml" encoding="utf-8" /> 
			<xsl:template match="/"><xsl:apply-templates /></xsl:template>
			<xsl:template match="search">
				<search>
					<xsl:element name="bid"><xsl:value-of select="bid"/></xsl:element>
					<xsl:element name="s_key_all"><xsl:value-of select="s_fname"/><xsl:text> </xsl:text><xsl:value-of select="s_lname"/><xsl:text> </xsl:text><xsl:value-of select="s_key_all"/></xsl:element>
					<xsl:element name="s_key_one"><xsl:value-of select="s_key_one"/></xsl:element>
					<xsl:element name="s_key_phrase"><xsl:value-of select="s_key_phrase"/></xsl:element>
					<xsl:element name="s_key_x"><xsl:value-of select="s_key_x"/></xsl:element>
				</search>
			</xsl:template>
			</xsl:stylesheet>
		</cfsavecontent>

		<cfreturn XMLParse(xmlTransform(arguments.stSearchXML,local.stXSL))>
	</cffunction>

</cfcomponent>