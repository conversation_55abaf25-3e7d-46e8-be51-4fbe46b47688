@charset "utf-8";
body { margin: 0; padding: 0; font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; color: #333; line-height: 1.5; font-weight: 400; font-size: 14px; }
*, input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
img { max-width: 100%; }
.wrapper a:focus { text-decoration: none; outline: none; }
a { color: #08c; text-decoration: underline; }
a:hover .BodyText, .BodyText a:hover, a:hover { color: #005580; }
a:hover .BodyText, a:active .BodyText { color: #e3121a; text-decoration: none; }
.tsAppBodyText { color: #333; }
/***Header***/
.header * { font-family: 'PT Sans', sans-serif; }
.header-in p { font-size: 30px; text-align: center; line-height: 27px; }
.header .brand { display: inline-block; float: none; font-size: 13px; padding: 0; margin: 0; margin-top: 4px; }
.header .navbar { margin-bottom: 0; }
.navbar .container { width: 1170px; }
.navIn { background: #002157; padding: 10px 10px 0; }
.header-in { background: #ffff; padding: 10px 0; }
.header-top-cols:last-child img { width: 225px; }
.header-top-cols { float: left; }
.navMain { background: #002157; padding: 20px 0; }
.header-in .header-top-cols { float: none; display: inline-block; vertical-align: middle; margin: 0 -2px; padding: 0 15px; }
.navMain .nav-collapse { border-top: 1px solid #fff; border-bottom: 1px solid #fff; }
.header .navbar-inner { background: #fff; border: none; -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; padding: 0; }
.header a { text-decoration: none; }
.navMain .caret { display: none; }
.header .navbar .nav li a { text-align: center; padding: 13px 24px 13px 0; line-height: normal; font-size: 13px; text-shadow: none; color: #fff; font-family: 'Roboto', sans-serif; font-weight: 400; text-decoration: none; text-transform: uppercase; -moz-transition: all ease 0.5s; -ms-transition: all ease 0.5s; -o-transition: all ease 0.5s; -webkit-transition: all ease 0.5s; transition: all ease 0.5s; position: relative }
.header .navbar .nav li.dropdown a:after { display: block; border-color: transparent transparent white; border-style: solid; border-width: 0 0 5px 5px; content: ""; height: 0; position: absolute; right: 14px; top: 24px; width: 0; }
.header .navbar .nav li:first-child a span { position: relative; }
.header .navbar .nav li:first-child a span:after { background-color: white; bottom: -7px; content: ''; width: 4px; height: 4px; border-radius: 50%; left: 50%; position: absolute; }
.header .navbar .nav li:last-child a { border-right: none; }
.header .navbar .nav li.active a { background: #fff; color: #444; }
.header .navbar .nav li a:hover, .header .navbar .nav li a:focus, .footer-menu li a:hover, .footer-menu li a:focus { background: #026694; }
.header .nav-collapse .nav { margin: 0; }
.navIcon .menu { display: none; }
.header .navbar .nav > li > .dropdown-menu::after, .header .navbar .nav > li > .dropdown-menu::before { display: none; }
.header .navbar .nav > li > .dropdown-menu::after, .header .navbar .nav > li > .dropdown-menu::before { display: none; }
.header .nav-collapse li:hover .dropdown-menu { display: block; }
.header .navbar .pull-right > li > .dropdown-menu, .header .navbar .nav > li > .dropdown-menu { left: 0; right: inherit; top: 34px; }
.dropdown-menu > li > a { background: #fff; }
.header .navbar .nav li .dropdown-menu > li > a { padding: 11px 20px 11px 15px; color: #033333; border-right: none; text-align: left; display: block; }
.header .dropdown-menu { -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; }
.header .navbar .nav li .dropdown-menu > li:last-child a { border-bottom: none; }
.header .navbar .nav li .dropdown-menu > li:hover a { background: transparent; color: #1790d1; }
.dropdown-menu { min-width: 200px; }
.header .navbar .pull-right > li > .dropdown-menu, .header .navbar .nav > li > .dropdown-menu { top: 39px; padding: 0; border: none; border: 1px solid #F0F0F0; border-radius: 0; }
.dropdown-submenu > .dropdown-menu { border: none; padding: 0; margin: 0; }
.header .dropdown-submenu .dropdown-menu { display: none !important; }
.header .dropdown-submenu:hover .dropdown-menu { display: block !important; }
.dropdown-submenu > a::after { display: none; }
.header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a { border: none; background: rgb(81,121,146); border-bottom: 1px solid #fff; }
.navbar .navMain .nav > li { transition: all ease-in-out 0.3s; }
.header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a:hover, .header .navbar .nav li a:hover, .header .navbar .nav li a:focus, .navbar .nav li.dropdown.open > .dropdown-toggle, .navbar .nav li.dropdown.active > .dropdown-toggle, .navbar .nav li.dropdown.open.active > .dropdown-toggle, .navbar .navMain .nav > li:hover { background: transparent; color: #1790d1; }
[data-toggle="dropdown"] {
 display: none;
}
/******Banner*****/
.banner { background: url(../img/banner_001.jpg) no-repeat; background-size: cover; width: 100%; min-height: 280px; background-position: center bottom; position: relative; }
.banner-text { position: absolute; left: 50%; bottom: 60px; -moz-transform: translateX(-50%); -ms-transform: translateX(-50%); -o-transform: translateX(-50%); -webkit-transform: translateX(-50%); transform: translateX(-50%); width: 1170px; text-align: center; color: #fff; width: 1170px; padding: 0 15px; }
.banner-text h1 { font-style: italic; margin: 0; }
/*****Footer****/
.footer { background: #BCBCBC; padding: 20px 0px; color: #000; font-size: .8em; text-align: center; }
/*****Content*****/

