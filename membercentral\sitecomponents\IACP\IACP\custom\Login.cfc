<cfcomponent>
	<cffunction name="showLoginForm" access="public" returntype="string" output="no">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<table width="70%" border="0" cellspacing="0" cellpadding="8" align="center" bgcolor="##ffffff" style="border:1px solid ##8D8D80;">
			<tr>
				<td colspan="2" class="login" style="text-align:center;background:##004080;color:##fceec8;font-family:times new roman;font-size:18px;">A4PC Education Login</td>
			</tr>
			<tr>
				<td valign="top" width="35%">
					<div style="line-height:1.5em;font-family: arial;font-size:14px;color:##004080;">
						<form method="post" action="/?pg=login" class="mc_form_login">
							#application.objUser.renderSecurityKeyElement()#
							Username:<br/>
							<input type="text" name="username"  id="username" size="25" value="#session.cfcuser.memberdata.username#"><br/>
							Password:<br/>
							<input type="password" name="password"  id="password" size="25" value=""><br/>
							<br/>
							<button type="submit" style="height:30px;" name="btnLogin">Login</button>
						</form>
						
					</div>
				</td>
				<td><div id="loginSponsorContainer" style="max-width:180px;margin-left:auto;margin-right:auto;"></div></td>
			</tr>
			<tr>
				<td colspan="2" valign="top" style="font-family: arial;font-size:12px;text-align:center;">
					<cfif arguments.event.getValue('showErrMessage',0) gt 0>
						<div id="loginErrorDIV" class="tsAppBodyText tsAppBodyTextImportant">
						<cfif arguments.event.getValue('showErrMessage') is 1>
							Login failed. Try again.
						<cfelseif arguments.event.getValue('showErrMessage') is 2>
							Your computer is not accepting cookies from our site. You must accept cookies in order to login.
						</cfif>
						</div>
					</cfif>
          <br /><br /><br />
          <a style="font-family: arial;font-size:12px;text-align:center;" href="/?pg=semwebCatalog">If you haven't registered, click here to view seminars and register.</a>
        </td>
			</tr>
      <tr>
      	<td colspan="2" style="text-align:center;">
        	<a style="font-family: arial;font-size:12px;text-align:center;" href="/?pg=login&logact=requestReset">If you forget your seminar login, click here to have your information sent by email.</a>
        </td>
			</table>
	
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.data>
  </cffunction>
	
</cfcomponent>