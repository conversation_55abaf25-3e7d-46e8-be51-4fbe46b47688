ALTER PROC dbo.general_PharmacySWODEnrollments
@participantID int,
@startdate datetime,
@enddate datetime,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int;

	-- set startdate to 00:00:00 of startdate, 00:00:00 of enddate
	SELECT @startdate = DATEADD(dd, DATEDIFF(dd,0,@startdate), 0);
	SELECT @enddate = DATEADD(dd, DATEDIFF(dd,0,dateadd(dd,1,@enddate)), 0);

	IF OBJECT_ID('tempdb..#tmpEntries') IS NOT NULL
		DROP TABLE #tmpEntries;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_IACP') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_IACP;
	IF OBJECT_ID('tempdb..#tmpMAResults_IACP') IS NOT NULL
		DROP TABLE #tmpMAResults_IACP;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_PPAG') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_PPAG;
	IF OBJECT_ID('tempdb..#tmpMAResults_PPAG') IS NOT NULL
		DROP TABLE #tmpMAResults_PPAG;
	IF OBJECT_ID('tempdb..##tmpSWODEnrollments') IS NOT NULL 
		DROP TABLE ##tmpSWODEnrollments;
	CREATE TABLE #tmpMAMemberIDs_IACP (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults_IACP (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpMAMemberIDs_PPAG (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMAResults_PPAG (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpEntries (autoID int IDENTITY(1,1), enrollmentID int, signedUpOrgCode varchar(10), seminarName varchar(250),
		depoMemberDataID int, memberID int, isTempMemberID bit);

	INSERT INTO #tmpEntries (enrollmentID, signedUpOrgCode, seminarName, depoMemberDataID, memberID, isTempMemberID)
	SELECT e.enrollmentID, p.orgcode, s.seminarName, d.depoMemberDataID, m.memberID, 0 AS isTempMemberID
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = e.participantID
	INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
	INNER JOIN dbo.tblSeminarsSWOD AS swod ON e.seminarID = swod.seminarID
	INNER JOIN dbo.tblSeminars as s on swod.seminarID = s.seminarID 
		AND s.participantID = @participantID
	INNER JOIN membercentral.dbo.ams_networkProfiles np ON d.depomemberdataID = np.depomemberdataID
	INNER JOIN membercentral.dbo.ams_memberNetworkProfiles mnp ON np.profileID = mnp.profileID
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = mnp.memberID
	WHERE e.dateEnrolled > @startdate
	AND e.dateEnrolled < @enddate
	AND e.isActive = 1
	AND (d.adminflag2 IS NULL OR d.adminflag2 <> 'Y')
		UNION
	SELECT e.enrollmentID, p.orgcode, s.seminarName, d.depoMemberDataID, m.memberID, 1 AS isTempMemberID
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = e.participantID
	INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
	INNER JOIN dbo.tblSeminarsSWOD AS swod ON e.seminarID = swod.seminarID
	INNER JOIN dbo.tblSeminars as s on swod.seminarID = s.seminarID 
		AND s.participantID = @participantID
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = d.MCmemberIDtemp
	WHERE e.dateEnrolled > @startdate
	AND e.dateEnrolled < @enddate
	AND e.isActive = 1
	AND (d.adminflag2 IS NULL OR d.adminflag2 <> 'Y');

	INSERT INTO #tmpMAMemberIDs_IACP (memberID)
	SELECT DISTINCT tmp.memberID
	FROM #tmpEntries AS tmp
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = tmp.memberID
		AND m.[status] <> 'D';

	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('IACP');
	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='nabpNumber,dateOfBirth',
		@membersTableName='#tmpMAMemberIDs_IACP', @membersResultTableName='#tmpMAResults_IACP';

	INSERT INTO #tmpMAMemberIDs_PPAG (memberID)
	SELECT DISTINCT tmp.memberID
	FROM #tmpEntries AS tmp
	INNER JOIN membercentral.dbo.ams_members m ON m.memberID = tmp.memberID
		AND m.[status] <> 'D'
	WHERE tmp.isTempMemberID = 0;

	SELECT @orgID = membercentral.dbo.fn_getOrgIDFromOrgCode('PPAG');
	EXEC membercentral.dbo.ams_getMemberDataByFields @orgID=@orgID, @fieldsList='NABP Number,Date of Birth',
		@membersTableName='#tmpMAMemberIDs_PPAG', @membersResultTableName='#tmpMAResults_PPAG';

	-- put data into temp table 
	;with rawdata as (
		SELECT TOP 100 PERCENT d.depoMemberDataID as depoMemberDataID
			, tmp.seminarName 
			, tmp.signedUpOrgCode as signedUpOn
			, CASE WHEN coalesce(IACPmd.memberID, PPAGmd.memberID) is NULL THEN 'No' ELSE 'Yes' END as [Member]
			, coalesce(IACPmd.memberID, PPAGmd.memberID) as [MemberID]
			, d.FirstName
			, d.LastName
			, d.billingfirm 
			, d.billingaddress
			, d.billingaddress2
			, d.billingcity 
			, d.billingstate
			, d.billingzip
			, d.phone
			, d.email
			, CASE WHEN coalesce(IACPmd.nabpNumber, PPAGmd.[NABP Number]) IS NULL THEN eac.idNumber
				ELSE coalesce(IACPmd.nabpNumber, PPAGmd.[NABP Number]) END AS nabpNumber
			, right('0' + rtrim(month(coalesce(IACPmd.dateOfBirth, PPAGmd.[Date of Birth]))),2) + right('0' + rtrim(day(coalesce(IACPmd.dateOfBirth, PPAGmd.[Date of Birth]))),2) AS [BirthDate]
			, convert(varchar(10),e.dateEnrolled,101) as dateEnrolled
			, convert(varchar(10),e.dateCompleted,101) as dateCompleted
		FROM #tmpEntries AS tmp
		INNER JOIN dbo.tblEnrollments AS e ON e.enrollmentID = tmp.enrollmentID
		INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = tmp.depoMemberDataID
		LEFT OUTER JOIN #tmpMAResults_IACP AS IACPmd ON IACPmd.memberID = tmp.memberID
		LEFT OUTER JOIN #tmpMAResults_PPAG AS PPAGmd ON PPAGmd.memberID = tmp.memberID
		LEFT OUTER JOIN tblEnrollmentsAndCredit AS eac ON eac.enrollmentID = e.enrollmentID
		WHERE tmp.isTempMemberID = 0

		UNION 

		SELECT TOP 100 PERCENT d.depoMemberDataID as depoMemberDataID
			, tmp.seminarName 
			, tmp.signedUpOrgCode as signedUpOn
			, CASE WHEN IACPmd.memberID is NULL THEN 'No' ELSE 'Yes' END as [Member]
			, CASE WHEN IACPmd.memberID is NULL THEN dbo.fn_csvSafeString('') ELSE IACPmd.memberID END as [MemberID]
			, d.FirstName 	
			, d.LastName 
			, d.billingfirm 
			, d.billingaddress 
			, d.billingaddress2
			, d.billingcity
			, d.billingstate
			, d.billingzip
			, d.phone 
			, d.email 
			, CASE WHEN IACPmd.nabpNumber IS NULL THEN eac.idNumber ELSE IACPmd.nabpNumber END AS nabpNumber
			, right('0' + rtrim(month(IACPmd.dateOfBirth)),2) + right('0' + rtrim(day(IACPmd.dateOfBirth)),2) AS [BirthDate]
			, convert(varchar(10),e.dateEnrolled,101) as dateEnrolled
			, convert(varchar(10),e.dateCompleted,101) as dateCompleted
		FROM #tmpEntries AS tmp
		INNER JOIN dbo.tblEnrollments AS e ON e.enrollmentID = tmp.enrollmentID
		INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
		INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = tmp.depoMemberDataID
		LEFT OUTER JOIN #tmpMAResults_IACP AS IACPmd ON IACPmd.memberID = tmp.memberID
		LEFT OUTER JOIN tblEnrollmentsAndCredit AS eac ON eac.enrollmentid = tmp.enrollmentID
		WHERE tmp.isTempMemberID = 1
	)
	SELECT depoMemberDataID as depoMemberDataID
		, dbo.fn_csvSafeString(seminarName) as [Title]
		, dbo.fn_csvSafeString(signedUpOn) as [SignedUpOn]
		, dbo.fn_csvSafeString(Member) as [Member]
		, dbo.fn_csvSafeString(MemberID) as [MemberID]
		, dbo.fn_csvSafeString(firstName) as [First Name]
		, dbo.fn_csvSafeString(lastName) as [Last Name]
		, dbo.fn_csvSafeString(billingfirm) as [Pharmacy/Company]
		, dbo.fn_csvSafeString(billingaddress) as [Address]
		, dbo.fn_csvSafeString(billingaddress2) as [Address2]
		, dbo.fn_csvSafeString(billingcity) as [City]
		, dbo.fn_csvSafeString(billingstate) as [State]
		, dbo.fn_csvSafeString(billingzip) as [Zip]
		, dbo.fn_csvSafeString(phone) as [Phone]
		, dbo.fn_csvSafeString(email) as [Email]
		, dbo.fn_csvSafeString(NABPNumber) as [NABP Number]
		, dbo.fn_csvSafeString(BirthDate) as [Birth Date]
		, dbo.fn_csvSafeString(dateEnrolled) as [Date Enrolled]
		, dbo.fn_csvSafeString(dateCompleted) as [Date Completed]
	INTO ##tmpSWODEnrollments
	FROM rawdata
	ORDER BY [Title], [Last Name], [First Name], [Email], [Date Enrolled];

	-- export data
	DECLARE @cmd varchar(6000);
	DECLARE @tmpBCP TABLE (theoutput varchar(max));
	set @cmd = 'bcp ##tmpSWODEnrollments out ' + @filename + ' -c -t, -T -S' + CAST(serverproperty('servername') as varchar(40));
	insert into @tmpBCP (theoutput)
	exec master..xp_cmdshell @cmd;

	-- return count of records
	SELECT count(*) AS returnCount
	FROM ##tmpSWODEnrollments;

	-- get fields returned
	EXEC tempdb.dbo.SP_COLUMNS ##tmpSWODEnrollments;

	IF OBJECT_ID('tempdb..#tmpEntries') IS NOT NULL
		DROP TABLE #tmpEntries;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_IACP') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_IACP;
	IF OBJECT_ID('tempdb..#tmpMAResults_IACP') IS NOT NULL
		DROP TABLE #tmpMAResults_IACP;
	IF OBJECT_ID('tempdb..#tmpMAMemberIDs_PPAG') IS NOT NULL
		DROP TABLE #tmpMAMemberIDs_PPAG;
	IF OBJECT_ID('tempdb..#tmpMAResults_PPAG') IS NOT NULL
		DROP TABLE #tmpMAResults_PPAG;
	IF OBJECT_ID('tempdb..##tmpSWODEnrollments') IS NOT NULL 
		DROP TABLE ##tmpSWODEnrollments;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
