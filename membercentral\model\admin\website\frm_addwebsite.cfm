<cfset local.addSiteUID = CreateUUID()>

<cfsavecontent variable="local.addWebsiteJS">
	<script type="text/javascript">
		function showLog() {
			<cfoutput>
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: 'Site Creation Status',
					iframe: true,
					contenturl: '#this.link.addSiteLog#&uid=#local.addSiteUID#&mode=direct'
				});
			</cfoutput>
		}
		function closeBox() {
			MCModalUtils.hideModal();
			<cfoutput>self.location.href = '#this.link.addSite#';</cfoutput>
		}
		function toggleOrgFieldsSection() {
			var orgrdo = $('input[name=frm_orgChoice]:checked').val();
			if (orgrdo == 'new') {
				$('#divOrgNew,#orgIdentityFieldsSection').removeClass('d-none');
				$('#divOrgExisting').addClass('d-none');
			} else {
				$('#divOrgNew,#orgIdentityFieldsSection').addClass('d-none');
				$('input.neworg').val('');
				$('#divOrgExisting').removeClass('d-none');
			}
		}
		function toggleNetworkFieldsSection() {
			var netrdo = $('input[name=frm_netChoice]:checked').val();
			if (netrdo == 'new') {
				$('#divNetNew').removeClass('d-none');
				$('#divNetExisting').addClass('d-none');
			} else {
				$('#divNetNew').addClass('d-none');
				$('input.newnet').val('');
				$('#divNetExisting').removeClass('d-none');
			}
		}
		function validateWebsiteForm() {
			var arrReq = new Array();
			mca_hideAlert('err_addwebsite');

			if ($('#frm_siteCode').val() == '') arrReq[arrReq.length] = "Enter a valid site code.";
			if ($('#frm_siteName').val() == '') arrReq[arrReq.length] = "Enter a valid site name.";

			var orgrdo = $("input[name=frm_orgChoice]:checked").val();
			if (orgrdo == 'new') {
				if ($('#frm_orgCode').val() == '') arrReq[arrReq.length] = "Enter a valid org code.";
				if ($('#frm_orgShortName').val() == '') arrReq[arrReq.length] = "Enter a valid org short name.";
				if ($('#frm_orgName').val() == '') arrReq[arrReq.length] = "Enter a valid org name.";

				if ($('#frm_identityAddress').val() == '') arrReq[arrReq.length] = "Enter a valid address for identity.";
				if ($('#frm_identityCity').val() == '') arrReq[arrReq.length] = "Enter a valid city for identity.";
				if ($('#frm_identityStateID').val() == '') arrReq[arrReq.length] = "Enter a valid state for identity.";
				if ($('#frm_identityPostalCode').val() == '') arrReq[arrReq.length] = "Enter a valid postal code for identity.";
				if ($('#frm_identityPhone').val() == '') arrReq[arrReq.length] = "Enter a valid phone for identity.";
				if ($('#frm_identityEmail').val() == '') arrReq[arrReq.length] = "Enter a valid email for identity.";
			}

			var netrdo = $("input[name=frm_netChoice]:checked").val();
			if (netrdo == 'new') {
				if ($('#frm_networkName').val() == '') arrReq[arrReq.length] = "Enter a valid network name.";
				if ($('#frm_supportProviderName').val() == '') arrReq[arrReq.length] = "Enter a valid network support provider name.";
				if ($('#frm_supportProviderPhone').val() == '') arrReq[arrReq.length] = "Enter a valid network support provider phone number.";
				if ($('#frm_supportProviderEmail').val() == '') arrReq[arrReq.length] = "Enter a valid network support provider e-mail address.";
			}

			if(arrReq.length){
				mca_showAlert('err_addwebsite', arrReq.join('<br/>'), true);
				return false;
			}

			showLog();
			$('#btnCreateSite').attr('disabled', true);
			return true;
		}
		function loadFromWufoo() {
			$('#frmSettings').trigger("reset");
			if($.trim($("#wufooEntryId").val()).length){
				$("#loadFromWufoo").html('<i class="fa-solid fa-spinner fa-spin"></i>').prop('disabled',true);
				var objParams = { wufooEntryId:$("#wufooEntryId").val() };
				$.getJSON('/?event=proxy.ts_json&c=SITE&m=getDataFromWufoo', objParams)
				.done(function(r) {
					if(typeof(r.results) == 'object' && r.results['EntryId'] !== undefined){
						$("#frm_siteCode").val(r.results.Field21);
						$("#frm_siteName").val(r.results.Field22);
						$("#frm_defaultTimeZoneID option").filter(function() { return r.results.Field24.length && ($.trim($(this).text()) == $.trim(r.results.Field24)); }).attr('selected','selected');
						$("#frm_defaultPostalStateID option").filter(function() { return r.results.Field26.length && $.trim($(this).text()).indexOf(r.results.Field26,0) == 0; }).attr('selected','selected');
						if(r.results.Field1 == "New Organization"){
							$("#frm_orgChoice_new").prop("checked", true).trigger('click');
							$("#frm_orgCode").val(r.results.Field5);
							$("#frm_orgShortName").val(r.results.Field6);
							$("#frm_orgName").val(r.results.Field7);
							$("#frm_identityPhone").val(r.results.Field17);
							$("#frm_identityEmail").val(r.results.Field18);
							$("#frm_identityAddress").val($.trim(r.results.Field42));
							$("#frm_identityAddress2").val($.trim(r.results.Field43));
							$("#frm_identityCity").val($.trim(r.results.Field44));
							$("#frm_identityStateID option[statecode='"+$.trim(r.results.Field45)+"']").eq(0).attr('selected','selected');
							$("#frm_identityPostalCode").val($.trim(r.results.Field46));
							$("#frm_defaultCurrencyType").val($.trim(r.results.Field49));
						}else{
							$("#frm_orgChoice_existing").prop("checked", true).trigger('click');
							$("#frm_orgID option").filter(function() { return r.results.Field3.length && $.trim($(this).text()).indexOf(r.results.Field3,0) == 0; }).attr('selected','selected');
						}

						if(r.results.Field28 == "Its Own Network"){
							$("#frm_netChoice_new").prop("checked", true).trigger('click');
							$("#frm_networkName").val(r.results.Field7);
							if(r.results.Field34 == "MemberCentral"){
								$("#frm_supportProviderName").val('MemberCentral');
								$("#frm_supportProviderPhone").val('************');
								$("#frm_supportProviderEmail").val('<EMAIL>');
								$("#frm_emailFrom option:contains('<EMAIL>')").attr('selected', 'selected');
							}else if(r.results.Field34 == "SeminarWeb"){
								$("#frm_supportProviderName").val('SeminarWeb');
								$("#frm_supportProviderPhone").val('************');
								$("#frm_supportProviderEmail").val('<EMAIL>');
								$("#frm_emailFrom option:contains('<EMAIL>')").attr('selected', 'selected');
							}
							else if(r.results.Field34 == "TrialSmith"){
								$("#frm_supportProviderName").val('TrialSmith');
								$("#frm_supportProviderPhone").val('************');
								$("#frm_supportProviderEmail").val('<EMAIL>');
								$("#frm_emailFrom option:contains('<EMAIL>')").attr('selected', 'selected');
							}
						}else{
							$("#frm_netChoice_existing").prop("checked", true).trigger('click');	
							$("#frm_networkID option").filter(function() { return r.results.Field31.length && $.trim($(this).text()).indexOf(r.results.Field31,0) == 0; }).attr('selected','selected');							
						}
					}
					$("#loadFromWufoo").html('Load from Wufoo').prop('disabled',false);
				})
				.fail(function(r) { 
					$("#loadFromWufoo").html('Load from Wufoo').prop('disabled',false);
				});
			}
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.addWebsiteJS)#">

<div class="d-flex">
	<div class="mr-auto pt-2 pb-2"><h4>Add Site to Platform</h4></div>
	<div class="pt-2 pb-2 pr-2"><input type="number" id="wufooEntryId" name="wufooEntryId" placeholder="Entry ID" class="form-control form-control-sm text-center" onkeypress="return (event.charCode == 8 || event.charCode == 0 || event.charCode == 13) ? null : event.charCode >= 48 && event.charCode <= 57" min="1" max="500"></div>
	<div class="pt-2 pb-2"><button class="btn btn-sm btn-primary float-right" type="button" id="loadFromWufoo" onClick="loadFromWufoo();" style="min-width:142px">Load from Wufoo</button></div>
</div>

<div id="err_addwebsite" class="alert alert-danger mb-2 d-none"></div>

<cfoutput>
<form name="frmSettings" id="frmSettings" method="POST" action="#this.link.insertSite#" onsubmit="return validateWebsiteForm()" target="ifrmProcessForm">
<input type="hidden" name="uid" id="uid" value="#local.addSiteUID#">
</cfoutput>

<div class="form-group row mt-2">
	<label for="frm_siteCode" class="col-sm-3 col-form-label-sm font-size-md">Site Code *</label>
	<div class="col-sm-2">
		<input type="text" name="frm_siteCode" id="frm_siteCode" value="" class="form-control form-control-sm" maxlength="10">
	</div>
</div>
<div class="form-group row">
	<label for="frm_siteName" class="col-sm-3 col-form-label-sm font-size-md">Site Name *</label>
	<div class="col-sm-9">
		<input type="text" name="frm_siteName" id="frm_siteName" value="" class="form-control form-control-sm" maxlength="60">
	</div>
</div>
<div class="form-group row">
	<label for="frm_defaultTimeZoneID" class="col-sm-3 col-form-label-sm font-size-md">Default Time Zone *</label>
	<div class="col-sm-9">
		<select name="frm_defaultTimeZoneID" id="frm_defaultTimeZoneID" class="form-control form-control-sm">
			<cfloop query="local.qryTimeZones">
				<cfoutput>
					<option value="#local.qryTimeZones.TimeZoneID#" <cfif local.qryTimeZones.TimeZoneID eq 6>selected</cfif>>#local.qryTimeZones.TimeZone#</option>
				</cfoutput>
			</cfloop>
		</select>
	</div>
</div>
<div class="form-group row">
	<label for="frm_defaultPostalStateID" class="col-sm-3 col-form-label-sm font-size-md">Default Postal State *</label>
	<div class="col-sm-9">
		<select name="frm_defaultPostalStateID" id="frm_defaultPostalStateID" class="form-control form-control-sm">
			<cfoutput query="local.qryStates" group="countryID">
				<optgroup label="#local.qryStates.country#">
				<cfoutput>
					<option value="#local.qryStates.stateCode#">#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
				</cfoutput>
				</optgroup>
			</cfoutput>
		</select>
	</div>
</div>
<div class="form-group row">
	<label for="frm_defaultCurrencyType" class="col-sm-3 col-form-label-sm font-size-md">Default Currency Type *</label>
	<div class="col-sm-9">
		<select name="frm_defaultCurrencyType" id="frm_defaultCurrencyType" class="form-control form-control-sm">
			<cfloop query="local.qryCurrencyTypes">
				<cfoutput><option value="#local.qryCurrencyTypes.currencyType#">#local.qryCurrencyTypes.currencyType#</option></cfoutput>
			</cfloop>
		</select>
	</div>
</div>
<div class="form-group row">
	<div class="offset-sm-3 col-sm-9">
		<div class="form-check">
			<input type="radio" name="frm_hasDepoTLA" id="frm_hasDepoTLA_1" value="1" class="form-check-input">
			<label for="frm_hasDepoTLA_1" class="form-check-label">Site already exists in DepoTLA.</label>
		</div>
		<div class="form-check">
			<input type="radio" name="frm_hasDepoTLA" id="frm_hasDepoTLA_0" value="0" class="form-check-input" checked>
			<label for="frm_hasDepoTLA_0" class="form-check-label">Site does not yet exist in DepoTLA.</label>
		</div>
	</div>
</div>
<div class="form-group row mt-4">
	<div class="col-sm-3">Organization</div>
	<div class="col-sm-9">
		<div class="form-check">
			<input type="radio" name="frm_orgChoice" id="frm_orgChoice_new" value="new" class="form-check-input" onclick="toggleOrgFieldsSection();" checked>
			<label for="frm_orgChoice_new" class="form-check-label">This site belongs to a new organization.</label>
			<div class="form-text my-2 ml-2" id="divOrgNew">
				<div class="form-group row">
					<label for="frm_orgCode" class="col-sm-3 col-form-label-sm font-size-md">Org Code *</label>
					<div class="col-sm-2">
						<input type="text" name="frm_orgCode" id="frm_orgCode" value="" class="form-control form-control-sm neworg" maxlength="10">
					</div>
				</div>
				<div class="form-group row">
					<label for="frm_orgShortName" class="col-sm-3 col-form-label-sm font-size-md">Org Short Name *</label>
					<div class="col-sm-9">
						<input type="text" name="frm_orgShortName" id="frm_orgShortName" value="" class="form-control form-control-sm neworg" maxlength="20">
					</div>
				</div>
				<div class="form-group row">
					<label for="frm_orgName" class="col-sm-3 col-form-label-sm font-size-md">Org Full Name *</label>
					<div class="col-sm-9">
						<input type="text" name="frm_orgName" id="frm_orgName" value="" class="form-control form-control-sm neworg" maxlength="100">
					</div>
				</div>

				<!--- identity fields --->
				<div class="card card-box my-3">
					<div class="card-header bg-light">
						<div class="card-header--title font-weight-bold font-size-sm">Identity Information</div>
					</div>
					<div class="card-body p-3">
						<div class="form-group row">
							<label for="frm_identityAddress" class="col-sm-3 col-form-label-sm font-size-md">Address *</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityAddress" id="frm_identityAddress" value="" class="form-control form-control-sm" maxlength="100">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityAddress" class="col-sm-3 col-form-label-sm font-size-md">Address 2</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityAddress2" id="frm_identityAddress2" value="" class="form-control form-control-sm" maxlength="100">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityCity" class="col-sm-3 col-form-label-sm font-size-md">City *</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityCity" id="frm_identityCity" value="" class="form-control form-control-sm" maxlength="75">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityStateID" class="col-sm-3 col-form-label-sm font-size-md">State *</label>
							<div class="col-sm-9">
								<select id="frm_identityStateID" name="frm_identityStateID" class="form-control form-control-sm">
									<option value=""></option>
									<cfoutput query="local.qryStates" group="countryID">
										<optgroup label="#local.qryStates.country#">
										<cfoutput>
											<option value="#local.qryStates.stateID#" stateCode="#local.qryStates.stateCode#">#local.qryStates.stateName#</option>
										</cfoutput>
										</optgroup>
									</cfoutput>
								</select>
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityPostalCode" class="col-sm-3 col-form-label-sm font-size-md">Postal Code *</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityPostalCode" id="frm_identityPostalCode" value="" class="form-control form-control-sm" maxlength="25">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityPhone" class="col-sm-3 col-form-label-sm font-size-md">Phone *</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityPhone" id="frm_identityPhone" value="" class="form-control form-control-sm" maxlength="40">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityPhone" class="col-sm-3 col-form-label-sm font-size-md">Fax</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityFax" id="frm_identityFax" value="" class="form-control form-control-sm" maxlength="40">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityEmail" class="col-sm-3 col-form-label-sm font-size-md">Email *</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityEmail" id="frm_identityEmail" value="" class="form-control form-control-sm" maxlength="255">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_identityWebsite" class="col-sm-3 col-form-label-sm font-size-md">Website</label>
							<div class="col-sm-9">
								<input type="text" name="frm_identityWebsite" id="frm_identityWebsite" value="" class="form-control form-control-sm" maxlength="400">
							</div>
						</div>
						<div class="form-group row">
							<label for="frm_XUserName" class="col-sm-3 col-form-label-sm font-size-md">X User Name</label>
							<div class="col-sm-9">
								<input type="text" name="frm_XUserName" id="frm_XUserName" value="" class="form-control form-control-sm" maxlength="100">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="form-check">
			<input type="radio" name="frm_orgChoice" id="frm_orgChoice_existing" value="existing" class="form-check-input" onclick="toggleOrgFieldsSection();">
			<label for="frm_orgChoice_existing" class="form-check-label">This site belongs to an existing organization.</label>
			<div class="form-text my-2 ml-2 d-none" id="divOrgExisting">
				<select name="frm_orgID" id="frm_orgID" class="form-control form-control-sm">
					<cfloop query="local.qryOrganizations">
						<cfoutput>
							<option value="#local.qryOrganizations.orgID#">#local.qryOrganizations.orgcombined#</option>
						</cfoutput>
					</cfloop>
				</select>
			</div>
		</div>
	</div>
</div>
<div class="form-group row mt-4">
	<div class="col-sm-3">Network</div>
	<div class="col-sm-9">
		<div class="form-check">
			<input type="radio" name="frm_netChoice" id="frm_netChoice_new" value="new" class="form-check-input" onclick="toggleNetworkFieldsSection();" checked>
			<label for="frm_netChoice_new" class="form-check-label">This site belongs to a new network.</label>
			<div class="form-text my-2 ml-2" id="divNetNew">
				<div class="form-group row">
					<label for="frm_networkName" class="col-sm-3 col-form-label-sm font-size-md">Network Name *</label>
					<div class="col-sm-9">
						<input type="text" name="frm_networkName" id="frm_networkName" value="" class="form-control form-control-sm newnet" maxlength="100">
					</div>
				</div>
				<div class="form-group row">
					<label for="frm_supportProviderName" class="col-sm-3 col-form-label-sm font-size-md">Support Provider Name *</label>
					<div class="col-sm-9">
						<input type="text" name="frm_supportProviderName" id="frm_supportProviderName" value="MemberCentral" class="form-control form-control-sm" maxlength="100">
					</div>
				</div>
				<div class="form-group row">
					<label for="frm_supportProviderPhone" class="col-sm-3 col-form-label-sm font-size-md">Support Provider Phone *</label>
					<div class="col-sm-9">
						<input type="text" name="frm_supportProviderPhone" id="frm_supportProviderPhone" value="************" class="form-control form-control-sm" maxlength="25">
					</div>
				</div>
				<div class="form-group row">
					<label for="frm_supportProviderEmail" class="col-sm-3 col-form-label-sm font-size-md">Support Provider E-mail *</label>
					<div class="col-sm-9">
						<input type="text" name="frm_supportProviderEmail" id="frm_supportProviderEmail" value="<EMAIL>" class="form-control form-control-sm" maxlength="100">
					</div>
				</div>
				<div class="form-group row">
					<label for="frm_emailFrom" class="col-sm-3 col-form-label-sm font-size-md">E-mail From</label>
					<div class="col-sm-9">
						<select name="frm_emailFrom" id="frm_emailFrom" class="form-control form-control-sm">
							<option value="<EMAIL>"><EMAIL></option>
							<option value="<EMAIL>"><EMAIL></option>
							<option value="<EMAIL>"><EMAIL></option>
						</select>
					</div>
				</div>
				<div class="form-group row mb-3">
					<div class="col-sm-3">Approvals</div>
					<div class="col-sm-9">
						<div class="form-check">
							<input type="checkbox" name="frm_addToApprovals" id="frm_addToApprovals" value="1" class="form-check-input">
							<label for="frm_addToApprovals" class="form-check-label">Add new accounts to the TrialSmith approvals queue.</label>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="form-check">
			<input type="radio" name="frm_netChoice" id="frm_netChoice_existing" value="existing" class="form-check-input" onclick="toggleNetworkFieldsSection();">
			<label for="frm_netChoice_existing" class="form-check-label">This site belongs to an existing network.</label>
			<div class="form-text my-2 ml-2 d-none" id="divNetExisting">
				<select name="frm_networkID" id="frm_networkID" class="form-control form-control-sm">
					<cfloop query="local.qryNetworks">
						<cfoutput>
							<option value="#local.qryNetworks.networkID#" <cfif local.qryNetworks.networkID eq 3>selected</cfif>>#local.qryNetworks.networkName#</option>
						</cfoutput>
					</cfloop>
				</select>
			</div>
		</div>
	</div>
</div>
<div class="form-group row mt-3">
	<div class="offset-sm-3 col-sm-9">
		<button type="submit" id="btnCreateSite" name="btnCreateSite" class="btn btn-sm btn-primary" >Create Site</button>
	</div>
</div>
</form>

<div class="d-none">
	<iframe name="ifrmProcessForm" id="ifrmProcessForm" height="1" width="1"></iframe>
</div>