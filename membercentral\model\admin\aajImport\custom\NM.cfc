<cfcomponent output="no">
	
	<!--- Subscriptions--->
	<cfset variables.NMPAC_subUID= "A4CBD70E-31E7-41F9-97CF-71195130EC8D" />	

	<!--- Rates --->
	<cfset variables.NMPAC_rateUID_CenturyClub = "8907AB5B-B66A-43BA-A019-CAAB13A883F2" />	
	<cfset variables.NMPAC_rateUID_COIRClub = "655E455C-E047-4427-8E59-73468C1308AF" />	
	<cfset variables.NMPAC_rateUID_RollOfHonor = "7500B8B3-55FB-4F9A-AFC7-F6A1C60E8BB5" />	
	<cfset variables.NMPAC_rateUID_NewPractitioner = "1A03B4FA-3611-4EDD-AD25-1877121BB07E" />
	<cffunction name="setAAJVars" access="public" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.aajIDColumnName = 'AAJ COIR ID'>
		<cfset local.frequencyShortNames = ['F','M','Q','S']>
		<cfset local.arrFiles = [ 
			{ num=1, filetype='P', payProfileCode='COIRAAJ', code='COIR-PAC' }
			]>
		<cfreturn local>
	</cffunction>

	<cffunction name="customChecks" access="public" output="false" returntype="string">
		<cfargument name="aajImportAdmin" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.orgcode = arguments.aajImportAdmin.orgcode>
		<cfset local.siteID = arguments.aajImportAdmin.siteid>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		
		<cfsavecontent variable="local.NonQualifyingRecords">
			<cfoutput>
			IF OBJECT_ID('tempdb..##tmpRateCheck') IS NOT NULL 
				EXEC('DROP TABLE ##tmpRateCheck') 
			select distinct memberID, memberName, import_aaj_id_num, serverFile, profileCode, frequencyShortName
			into ##tmpRateCheck
			from ####tmpFinalAAJ#local.orgcode#
			where memberID <> 0;

			declare @FID int, @siteID int;
			set @FID = #local.qualifySubRateRFID#;
			set @siteID = #local.siteID#;

			declare @NMPAC_subUID varchar(100), @NMPAC_subName varchar(255)
			declare @NMPAC_rate_CenturyClub_UID varchar(100), @NMPAC_rate_COIRClub_UID varchar(100), @NMPAC_rate_RollOfHonor_UID varchar(100), @NMPAC_rate_NewPractitioner_UID varchar(100)

			set @NMPAC_subUID = '#variables.NMPAC_subUID#'
			set @NMPAC_rate_CenturyClub_UID = '#variables.NMPAC_rateUID_CenturyClub#'
			set @NMPAC_rate_COIRClub_UID = '#variables.NMPAC_rateUID_COIRClub#'
			set @NMPAC_rate_RollOfHonor_UID = '#variables.NMPAC_rateUID_RollOfHonor#'
			set @NMPAC_rate_NewPractitioner_UID = '#variables.NMPAC_rateUID_NewPractitioner#'
			select @NMPAC_subName=subscriptionName from sub_subscriptions where uid = @NMPAC_subUID

			IF OBJECT_ID('tempdb..##tmpNonQual') IS NOT NULL 
				EXEC('DROP TABLE ##tmpNonQual') 

			select memberID, memberName, import_aaj_id_num, serverFile, profileCode
			into ##tmpNonQual
			from (
				select memberID, memberName, import_aaj_id_num, serverFile, profileCode
				from (					
					select jdn2.memberID, jdn2.memberName, jdn2.import_aaj_id_num, jdn2.serverFile, jdn2.profileCode, 
						count(x.subscriptionID) as subCount, count(x.rateID) as rateCount
					from ##tmpRateCheck as jdn2
					left outer join (
						select m.memberID, r.rateName, r.rateID, subs.subscriptionID, subs.subscriptionName 
						from dbo.ams_members as m 
						inner join ##tmpRateCheck as jdn on jdn.memberID = m.memberID and jdn.profileCode = '#arguments.aajImportAdmin.aajVars.arrFiles[1].payProfileCode#'
						inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.groupPrintID = m.groupPrintID and gprp.siteID = @siteID
						inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.rightPrintID = gprp.rightPrintID AND srfrp.functionID = @FID and srfrp.siteID = @siteID 
						inner join dbo.sub_rates as r on r.siteResourceID = srfrp.siteResourceID 
							and r.status = 'A' 
							and r.isRenewalRate = 0 
							and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0) 
							and r.uid in (@NMPAC_rate_CenturyClub_UID, @NMPAC_rate_COIRClub_UID, @NMPAC_rate_RollOfHonor_UID, @NMPAC_rate_NewPractitioner_UID)
						inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID and rs.status = 'A'
						inner join dbo.sub_subscriptions as subs on subs.scheduleID = r.scheduleID 
							and subs.status = 'A' 
							and subs.uid = @NMPAC_subUID
						inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.status = 'A' 
						inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.status = 'A' and f.frequencyShortName = jdn.frequencyShortName
						inner join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfID and rfmp.status = 'A' 
						inner join dbo.mp_profiles as mp on mp.profileID = rfmp.profileID and mp.status = 'A' and mp.profileCode = jdn.profileCode
						group by m.memberID, r.rateName, r.rateID, subs.subscriptionID, subs.subscriptionName 
					) x on jdn2.memberID = x.memberID
					where jdn2.profileCode = '#arguments.aajImportAdmin.aajVars.arrFiles[1].payProfileCode#'
				group by jdn2.memberID, jdn2.memberName, jdn2.import_aaj_id_num, jdn2.serverFile, jdn2.profileCode
				) as y
				where (subCount <> 1) OR (rateCount <> 1) 
			) as z

			IF EXISTS (select top 1 memberid from ##tmpNonQual) 
			BEGIN
				select 'NONQUAL' as errorCode, memberID, memberName, import_aaj_id_num, serverFile
				from ##tmpNonQual
				order by serverFile, memberName
				
				IF OBJECT_ID('tempdb..##tmpNonQual') IS NOT NULL 
					EXEC('DROP TABLE ##tmpNonQual') 
				IF OBJECT_ID('tempdb..##tmpRateCheck') IS NOT NULL 
					EXEC('DROP TABLE ##tmpRateCheck') 

				goto on_done
			END 
			ELSE 
			BEGIN
				IF OBJECT_ID('tempdb..##tmpNonQual') IS NOT NULL 
					EXEC('DROP TABLE ##tmpNonQual') 
				IF OBJECT_ID('tempdb..##tmpRateCheck') IS NOT NULL 
					EXEC('DROP TABLE ##tmpRateCheck') 
			END
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.NonQualifyingRecords>
	</cffunction>
	
	<cffunction name="doSubsAndPayments" access="public" output="false" returntype="struct">
		<cfargument name="aajImportAdmin" type="struct" required="true" />
		<cfargument name="event" type="any" required="yes">
		<cfargument name="qryImport" type="query" required="true" />
		<cfargument name="batchDt" type="string" required="yes">
		<cfargument name="fOverrideDate" type="string" required="yes">
		
		<cfset var local = structNew() />
		<cfset local.rs = { arrSkippedValues=arrayNew(1), arrErrorValues=arrayNew(1), arrProcessedValues=arrayNew(1), arrPayErrorValues=arrayNew(1), isErr=0, errMsg='' } />
		<cfset local.objSubReg = createObject("component","model.admin.subscriptions.SubscriptionReg") />

		<!--- should only be good members at this point, but this check helps catch if not --->
		<cftry>
			<cfquery name="local.qryImport2" dbtype="query">
				select memberID, memberName, import_aaj_id_num, serverFile, profileCode, frequencyShortName, description, sum(amount) as amount
				from arguments.qryImport
				group by memberID, memberName, import_aaj_id_num, serverFile, profileCode, frequencyShortName, description
			</cfquery>

			<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

			<cfquery name="local.qrySubscriptions" datasource="#application.dsn.membercentral.dsn#" result="local.qrySubscriptionsResult">
				set nocount on
				
				IF OBJECT_ID('tempdb..##tempNMSub') IS NOT NULL 
					EXEC('DROP TABLE ##tempNMSub')				

				create table ##tempNMSub (
					memberID int, 
					memberName varchar(max), 
					subUID varchar(100), 
					subName varchar(100), 
					rateUID varchar(100), 
					rateName varchar(100), 
					description varchar(max), 
					amount decimal(18,2), 
					frequencyShortName varchar(10), 
					profileCode varchar(100)
				)

				create table ##tempNMJNTest (
					id int identity(1,1), 
					memberID int, 
					memberName varchar(max), 
					import_aaj_id_num varchar(max), 
					serverFile varchar(max), 
					profileCode varchar(100), 
					frequencyShortName varchar(10), 
					description varchar(max), 
					amount decimal(18,2)
				)

				<cfloop query="local.qryImport2">
					insert into ##tempNMJNTest(memberID, memberName, import_aaj_id_num, serverFile, profileCode, frequencyShortName, description, amount)
					values (#local.qryImport2.memberID#, '#local.qryImport2.memberName#', '#local.qryImport2.import_aaj_id_num#', '#local.qryImport2.serverFile#', '#local.qryImport2.profileCode#', '#local.qryImport2.frequencyShortName#', '#local.qryImport2.description#', '#local.qryImport2.amount#')
				</cfloop>

				declare @FID int, @siteID int
				declare @NMPAC_subUID varchar(100), @NMPAC_subName varchar(255)
				declare @NMPAC_rate_CenturyClub_UID varchar(100), @NMPAC_rate_COIRClub_UID varchar(100), @NMPAC_rate_RollOfHonor_UID varchar(100), @NMPAC_rate_NewPractitioner_UID varchar(100)

				set @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue("mc_siteinfo.siteID")#">
				set @NMPAC_subUID = <cfqueryparam value="#variables.NMPAC_subUID#" cfsqltype="cf_sql_varchar" />
				select @NMPAC_subName=subscriptionName from sub_subscriptions where uid = @NMPAC_subUID
				set @NMPAC_rate_CenturyClub_UID = <cfqueryparam value="#variables.NMPAC_rateUID_CenturyClub#" cfsqltype="cf_sql_varchar" />
				set @NMPAC_rate_COIRClub_UID = <cfqueryparam value="#variables.NMPAC_rateUID_COIRClub#" cfsqltype="cf_sql_varchar" />
				set @NMPAC_rate_RollOfHonor_UID = <cfqueryparam value="#variables.NMPAC_rateUID_RollOfHonor#" cfsqltype="cf_sql_varchar" />
				set @NMPAC_rate_NewPractitioner_UID = <cfqueryparam value="#variables.NMPAC_rateUID_NewPractitioner#" cfsqltype="cf_sql_varchar" />

				insert into ##tempNMSub (memberID, memberName, subUID, subName, rateUID, rateName, description, amount, frequencyShortName, profileCode)
				select memberID, memberName, subUID, subName, rateUID, rateName, description, amount, frequencyShortName, profileCode
				from (
					select jdn2.memberID, 
						jdn2.memberName, 
						count(distinct x.subscriptionID) as subCount, 
						x.subUID, 
						dbo.pipeList(distinct x.subscriptionName) as subName, 
						count(distinct x.rateID) as rateCount, 
						x.rateUID, 
						dbo.pipeList(distinct x.rateName) as rateName,
						dbo.pipeList(jdn2.description) as description, 
						sum(jdn2.amount) as amount,
						jdn2.frequencyShortName, 
						jdn2.profileCode
					from ##tempNMJNTest as jdn2
					left outer join (
						select m.memberID, r.rateName, r.rateID, r.uid as rateUID, subs.subscriptionID, subs.uid as subUID, subs.subscriptionName, jdn.id as rowID
						from dbo.ams_members as m 
						inner join ##tempNMJNTest as jdn on jdn.memberID = m.memberID and jdn.profileCode = 'COIRAAJ'
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.groupPrintID = m.groupPrintID and gprp.siteID = @siteID
						INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.rightPrintID = gprp.rightPrintID AND srfrp.functionID = @FID and srfrp.siteID = @siteID  
						inner join dbo.sub_rates as r on r.siteResourceID = srfrp.siteResourceID 
							and r.status = 'A' 
							and r.isRenewalRate = 0 
							and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0) 
							and r.uid in (@NMPAC_rate_CenturyClub_UID, @NMPAC_rate_COIRClub_UID, @NMPAC_rate_RollOfHonor_UID, @NMPAC_rate_NewPractitioner_UID)
						inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID and rs.status = 'A'
						inner join dbo.sub_subscriptions as subs on subs.scheduleID = r.scheduleID and subs.status = 'A' and subs.uid = @NMPAC_subUID
						inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.status = 'A' 
						inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.status = 'A' and f.frequencyShortName = jdn.frequencyShortName 
						inner join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfID and rfmp.status = 'A' 
						inner join dbo.mp_profiles as mp on mp.profileID = rfmp.profileID and mp.status = 'A' and mp.profileCode = jdn.profileCode 
					) x on jdn2.memberID = x.memberID and jdn2.id = x.rowID
					where jdn2.profileCode = 'COIRAAJ'
					group by jdn2.memberID, jdn2.memberName, x.subUID, x.rateUID, jdn2.frequencyShortName, jdn2.profileCode
				) y
				where (subCount = 1) AND (rateCount = 1)

				select memberID, frequencyShortName, subUID, subName, rateUID, rateName, description, amount, profileCode
				from ##tempNMSub 
				order by memberName, profileCode
				
				IF OBJECT_ID('tempdb..##tempNMSub') IS NOT NULL 
					EXEC('DROP TABLE ##tempNMSub')
					
				set nocount off				
			</cfquery>	

			<cfquery name="local.qryFrequency" datasource="#application.dsn.membercentral.dsn#">
				select UPPER(uid) as uid
				from dbo.sub_frequencies
				where siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="cf_sql_integer" />
				and frequencyShortName = 'F'
			</cfquery>
			<cfloop query="local.qrySubscriptions">

				<cfset local.currMember = { memberID=local.qrySubscriptions.memberID, 
											totalAmount=local.qrySubscriptions.amount, 
											frequencyShortName=local.qrySubscriptions.frequencyShortName, 
											liftLevel=local.qrySubscriptions.subName, 
											hasAAJ=false, aajPrice=0,
											hasPAC=false, pacPrice=0,
											hasASC=false, ascPrice=0 } />
											
				<cfset local.currMember.hasPAC = true />
				<cfset local.currMember.PACPrice = local.qrySubscriptions.amount />

				<cfset local.subStruct = structNew() />
				<cfset local.subStruct.uid = local.qrySubscriptions.subUID />
				<cfset local.subStruct.rateUID = local.qrySubscriptions.rateUID />
				<cfset local.subStruct.freqUID = local.qryFrequency.uid />
				<cfset local.subStruct.rateOverride = local.qrySubscriptions.amount />
				
				<cfif NOT Len(local.subStruct.uid)>
					<cfset arrayAppend(local.rs.arrSkippedValues, local.currMember) />
				<cfelse>
					<cfset local.subReturn = local.objSubReg.autoSubscribe(	event=arguments.event, 
																			memberID=local.currMember.memberID, 
																			subStruct=local.subStruct, 
																			startDateOverride=arguments.fOverrideDate,
																			saleDateOverride=arguments.batchDt)>
				
					<cfif NOT local.subReturn.success>
						<cfset arrayAppend(local.rs.arrErrorValues, local.currMember) />
					<cfelse>
						<cfset arrayAppend(local.rs.arrProcessedValues, local.currMember) />
	
						<cfset local.currMember.rootSubscriberID = local.subReturn.rootSubscriberID />
						<cfset local.currMember.subStruct = local.subStruct />
						
						<cfquery name="local.qryCreatedSubs" datasource="#application.dsn.membercentral.dsn#">
							set nocount on
							
							declare @subStartDate datetime, @subEndDate dateTime, @subGraceEndDate dateTime
							set @subEndDate = NULL
							set @subGraceEndDate = NULL
							
							select @subStartDate = subStartDate
							from dbo.sub_subscribers
							where subscriberID = <cfqueryparam value="#val(local.currMember.rootSubscriberID)#" cfsqltype="cf_sql_integer" />
							
							<!--- fix dates --->
							<cfif left(local.qrySubscriptions.frequencyShortName,1) eq "M">
								set @subEndDate = DateAdd(ms, -3, DateAdd(mm, 1, @subStartDate))
							<cfelseif left(local.qrySubscriptions.frequencyShortName,1) eq "Q">
								set @subEndDate = DateAdd(ms, -3, DateAdd(mm, 3, @subStartDate))
							<cfelseif left(local.qrySubscriptions.frequencyShortName,1) eq "S">
								set @subEndDate = DateAdd(ms, -3, DateAdd(mm, 6, @subStartDate))
							<cfelseif left(local.qrySubscriptions.frequencyShortName,1) eq "F">
								set @subEndDate = DateAdd(ms, -3, DateAdd(mm, 12, @subStartDate))
							</cfif>
							
							IF @subEndDate is not null
							begin
								update dbo.sub_subscribers
								set subEndDate = @subEndDate,
									graceEndDate = DateAdd(ms, -3, DateAdd(dd, 15, @subEndDate)),
									expectedSubendDate = @subEndDate,
									expectedGraceEndDate = DateAdd(ms, -3, DateAdd(dd, 15, @subEndDate))
								where rootSubscriberID = <cfqueryparam value="#val(local.currMember.rootSubscriberID)#" cfsqltype="cf_sql_integer" />
							end
							
							set nocount off
						</cfquery>
											
						<cfset local.batchReturn = arguments.aajImportAdmin.aajVars.strBatches[local.qrySubscriptions.profileCode]>

						<!--- Look up invoice just created, mark it closed, record payment, apply payment to invoice --->
						<cfquery name="local.qryCloseInvoice" datasource="#application.dsn.membercentral.dsn#">
							set nocount on
							
							declare 
								@orgID int, @siteID int, 
								@enteredByMemberID int, @statsSessionID int, @invoiceID int, 
								@rc int, @historyID int, @paymentTransactionID int, 
								@dtNow dateTime, @dtPayment dateTime, @invAmount decimal(18,2),
								@success bit, @invAmountStr varchar(10)
								
							set @success = 1
							select @dtNow = getdate()
							select @dtPayment = <cfqueryparam value="#arguments.batchDt# #timeformat(now(),'h:mm tt')#" cfsqltype="cf_sql_timestamp" />
							select @orgID = <cfqueryparam value="#arguments.event.getValue("mc_siteinfo.orgID")#" cfsqltype="cf_sql_integer" />
							select @siteID = <cfqueryparam value="#arguments.event.getValue("mc_siteinfo.siteID")#" cfsqltype="cf_sql_integer" />
							select @enteredByMemberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#"  cfsqltype="cf_sql_integer" />
							select @statsSessionID = <cfqueryparam value="#val(session.cfcUser.statsSessionID)#"  cfsqltype="cf_sql_integer" />
							
							BEGIN TRAN
							BEGIN TRY
							
							select 
								@invoiceID = it.invoiceID, 
								@invAmount = sum(cache_invoiceAmountAfterAdjustment)
							from 
								dbo.tr_invoiceTransactions as it
								inner join dbo.tr_applications as ta on ta.orgID = @orgID and ta.transactionID = it.transactionID
									and ta.applicationTypeID = 17
									and ta.itemType = 'Dues'
									and ta.status = 'A'
								inner join dbo.sub_subscribers as s on 
									s.subscriberID = ta.itemID
									and s.rootSubscriberID = <cfqueryparam value="#local.currMember.rootSubscriberID#" cfsqltype="cf_sql_integer" />
							where it.orgID = @orgID
							group by 
								it.invoiceID
							
							select @invAmountStr = cast(@invAmount as varchar(10))
	
								exec dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID
				
								insert into dbo.tr_paymentHistory (paymentInfo, gatewayResponse, datePaid, statsSessionID, gatewayTransactionID, 
									gatewayApprovalCode, payerMemberID, memberPaymentProfileID, isSuccess,
									gatewayID, profileID, paymentType, responseReasonCode, orgID)
		              			values (
		              				cast('<payment gatewayid="#local.batchReturn.payProfileGatewayID#" profileid="#local.batchReturn.payProfileID#"><args><x_amount>'+@invAmountStr+'</x_amount><x_description>AAJ Payment</x_description><fld_19_>#local.qrySubscriptions.description#</fld_19_></args><gateway><x_amount>'+@invAmountStr+'</x_amount><x_description>AAJ Payment</x_description><fld_19_>#local.qrySubscriptions.description#</fld_19_></gateway></payment>' as xml),
									cast('<response><rawresponse/><responsecode>1</responsecode><responsereasontext/><responsereasoncode/><transactionid>0</transactionid><approvalcode/><transactiondetail/><status>Active</status><glaccountid>#local.batchReturn.payprofileGLAccountID#</glaccountid><historyid/></response>' as xml),
		                      		@dtPayment, @statsSessionID, '0', '0', <cfqueryparam value="#local.currMember.memberID#"  cfsqltype="cf_sql_integer" />, null, 1,
									#local.batchReturn.payProfileGatewayID#, #local.batchReturn.payProfileID#, 'payment', null, @orgID
								)
									select @historyID = SCOPE_IDENTITY()
	
								exec dbo.tr_createTransaction_payment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
									@assignedToMemberID=<cfqueryparam value="#local.currMember.memberID#"  cfsqltype="cf_sql_integer" />, 
									@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID, @status='Active', @detail='AAJ Payment', 
									@amount=@invAmount, @transactionDate=@dtPayment, 
									@debitGLAccountID=<cfqueryparam value="#local.batchReturn.payprofileGLAccountID#"  cfsqltype="cf_sql_integer" />, 
									@profileID=<cfqueryparam value="#local.batchReturn.payProfileID#"  cfsqltype="cf_sql_integer" />, 
									@historyID=@historyID, @batchid=<cfqueryparam value="#local.batchReturn.batchID#"  cfsqltype="cf_sql_integer" />, 
									@offeredPaymentFee=0, @isApplePay=0, @isGooglePay=0, @transactionID=@paymentTransactionID OUTPUT;
				
								exec dbo.tr_allocateToInvoice @recordedOnSiteID=@siteID, @recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID, 
									@amount=@invAmount, @transactionDate=@dtNow, @paymentTransactionID=@paymentTransactionID, @invoiceID=@invoiceID;

							COMMIT TRAN
							SELECT 1 as success

							END TRY
							BEGIN CATCH
								ROLLBACK TRAN
								SELECT 0 as success			
							END CATCH
						</cfquery>
				
						<cfif local.qryCloseInvoice.success neq 1>
							<cfset ArrayAppend(local.rs.arrPayErrorValues, local.currMember) />
						</cfif>
					</cfif>
				</cfif>
			
			</cfloop>

			<cfcatch type="any">
				<cfset local.rs.isErr = 1 />
				<cfset local.rs.errMsg = "There was a problem creating the subscriptions or recording the transactions. Contact us for assistance." />
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
				<cfreturn local.rs />
			</cfcatch>			
		</cftry>

		<cfreturn local.rs />
	</cffunction>
	
	
</cfcomponent>